/**
 * WCAG API Request/Response Schemas
 * Strict validation schemas using Zod - NO any[] types
 */

import { z } from 'zod';

// Base schemas
const WcagVersionSchema = z.enum(['2.1', '2.2', '3.0', 'all']);
const WcagLevelSchema = z.enum(['A', 'AA', 'AAA']);
const ScanStatusSchema = z.enum(['pending', 'running', 'completed', 'failed', 'cancelled']);

// Scan request schema
export const WcagScanRequestSchema = z.object({
  targetUrl: z.string().url('Invalid URL format').max(2048, 'URL too long'),
  scanOptions: z
    .object({
      enableContrastAnalysis: z.boolean().optional().default(true),
      enableKeyboardTesting: z.boolean().optional().default(true),
      enableFocusAnalysis: z.boolean().optional().default(true),
      enableSemanticValidation: z.boolean().optional().default(true),
      enableManualReview: z.boolean().optional().default(false),
      wcagVersion: WcagVersionSchema.optional().default('all'),
      level: WcagLevelSchema.optional().default('AA'),
      maxPages: z.number().int().min(1).max(10).optional().default(5),
      timeout: z.number().int().min(5000).max(60000).optional().default(30000),
    })
    .optional()
    .default({}),
});

// Scan response schema
export const WcagScanResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    scanId: z.string().uuid(),
    targetUrl: z.string().url(),
    status: ScanStatusSchema,
    overallScore: z.number().int().min(0).max(100).optional(),
    levelAchieved: z.enum(['A', 'AA', 'AAA', 'FAIL']).optional(),
    riskLevel: z.enum(['low', 'medium', 'high', 'critical']).optional(),
    // AUTOMATED SUMMARY ONLY - strict separation
    automatedSummary: z
      .object({
        totalAutomatedChecks: z.number().int().min(0),
        passedAutomatedChecks: z.number().int().min(0),
        failedAutomatedChecks: z.number().int().min(0),
        automatedScore: z.number().int().min(0).max(100), // Single automated score
        categoryScores: z.object({
          perceivable: z.number().int().min(0).max(100),
          operable: z.number().int().min(0).max(100),
          understandable: z.number().int().min(0).max(100),
          robust: z.number().int().min(0).max(100),
        }),
        versionScores: z.object({
          wcag21: z.number().int().min(0).max(100),
          wcag22: z.number().int().min(0).max(100),
          wcag30: z.number().int().min(0).max(100),
        }),
        automationRate: z.number().min(0).max(1),
      })
      .optional(),
    // MANUAL REVIEW SUMMARY - separate tracking
    manualReviewSummary: z.object({
      totalManualItems: z.number().int().min(0),
      pendingReviews: z.number().int().min(0),
      completedReviews: z.number().int().min(0),
      estimatedReviewTime: z.number().int().min(0), // minutes
    }),
    // AUTOMATED CHECKS ONLY - contribute to scoring
    automatedChecks: z
      .array(
        z.object({
          ruleId: z.string(),
          ruleName: z.string(),
          category: z.enum(['perceivable', 'operable', 'understandable', 'robust']),
          wcagVersion: WcagVersionSchema,
          successCriterion: z.string(),
          level: WcagLevelSchema,
          status: z.enum(['passed', 'failed', 'not_applicable']), // NO manual_review
          score: z.number().int().min(0).max(100),
          maxScore: z.number().int().min(0).max(100),
          weight: z.number().min(0).max(1),
          evidence: z.array(
            z.object({
              type: z.enum(['text', 'image', 'code', 'measurement', 'interaction']),
              description: z.string(),
              value: z.string(),
              selector: z.string().optional(),
              screenshot: z.string().optional(),
              severity: z.enum(['info', 'warning', 'error', 'critical']),
            }),
          ),
          recommendations: z.array(z.string()),
          executionTime: z.number().int().min(0),
          errorMessage: z.string().optional(),
        }),
      )
      .optional(),
    // MANUAL REVIEW ITEMS - separate tracking
    manualReviewItems: z
      .array(
        z.object({
          ruleId: z.string(),
          description: z.string(),
          priority: z.enum(['high', 'medium', 'low']),
          estimatedTime: z.number().int().min(0), // minutes
          status: z.enum(['pending', 'in_progress', 'completed', 'skipped']),
          reviewerNotes: z.string().optional(),
        }),
      )
      .optional(),
    recommendations: z
      .array(
        z.object({
          ruleId: z.string(),
          priority: z.enum(['high', 'medium', 'low']),
          category: z.enum(['perceivable', 'operable', 'understandable', 'robust']),
          title: z.string(),
          description: z.string(),
          implementation: z.string(),
          resources: z.array(z.string()),
        }),
      )
      .optional(),
    metadata: z
      .object({
        scanId: z.string().uuid(),
        userId: z.string(),
        requestId: z.string().uuid(),
        startTime: z.string().datetime(),
        endTime: z.string().datetime().optional(),
        duration: z.number().int().min(0).optional(),
        userAgent: z.string(),
        viewport: z.object({
          width: z.number().int().min(1),
          height: z.number().int().min(1),
        }),
        environment: z.string(),
        version: z.string(),
      })
      .optional(),
  }),
  requestId: z.string().uuid(),
  processingTime: z.number().int().min(0),
});

// Scan list response schema
export const WcagScanListResponseSchema = z.object({
  success: z.boolean(),
  data: z.object({
    scans: z.array(
      z.object({
        scanId: z.string().uuid(),
        targetUrl: z.string().url(),
        status: ScanStatusSchema,
        overallScore: z.number().int().min(0).max(100).optional(),
        levelAchieved: z.enum(['A', 'AA', 'AAA', 'FAIL']).optional(),
        riskLevel: z.enum(['low', 'medium', 'high', 'critical']).optional(),
        scanTimestamp: z.string().datetime(),
        completionTimestamp: z.string().datetime().optional(),
        totalAutomatedChecks: z.number().int().min(0).optional(),
        passedAutomatedChecks: z.number().int().min(0).optional(),
        failedAutomatedChecks: z.number().int().min(0).optional(),
        manualReviewItems: z.number().int().min(0).optional(), // Count only, no scoring
      }),
    ),
    pagination: z.object({
      page: z.number().int().min(1),
      limit: z.number().int().min(1).max(100),
      total: z.number().int().min(0),
      totalPages: z.number().int().min(0),
    }),
  }),
  requestId: z.string().uuid(),
  processingTime: z.number().int().min(0),
});

// Export request schema
export const WcagExportRequestSchema = z.object({
  scanId: z.string().uuid('Invalid scan ID format'),
  format: z.enum(['pdf', 'json', 'csv']).default('pdf'),
  includeEvidence: z.boolean().optional().default(true),
  includeRecommendations: z.boolean().optional().default(true),
  includeManualReviewItems: z.boolean().optional().default(true), // Include manual review tracking
});

// Error response schema
export const WcagErrorResponseSchema = z.object({
  success: z.literal(false),
  error: z.object({
    code: z.string(),
    message: z.string(),
    details: z.record(z.unknown()).optional(),
    ruleId: z.string().optional(),
    context: z.string().optional(),
  }),
  requestId: z.string().uuid(),
  processingTime: z.number().int().min(0),
});

// Query parameters schemas
export const WcagScanListQuerySchema = z.object({
  page: z.coerce.number().int().min(1).optional().default(1),
  limit: z.coerce.number().int().min(1).max(100).optional().default(20),
  status: ScanStatusSchema.optional(),
  startDate: z.string().datetime().optional(),
  endDate: z.string().datetime().optional(),
  sortBy: z
    .enum(['scanTimestamp', 'overallScore', 'targetUrl'])
    .optional()
    .default('scanTimestamp'),
  sortOrder: z.enum(['asc', 'desc']).optional().default('desc'),
});

// Type exports for use in API handlers
export type WcagScanRequest = z.infer<typeof WcagScanRequestSchema>;
export type WcagScanResponse = z.infer<typeof WcagScanResponseSchema>;
export type WcagScanResult = WcagScanResponse; // Alias for backward compatibility
export type WcagScanListResponse = z.infer<typeof WcagScanListResponseSchema>;
export type WcagExportRequest = z.infer<typeof WcagExportRequestSchema>;
export type WcagErrorResponse = z.infer<typeof WcagErrorResponseSchema>;
export type WcagScanListQuery = z.infer<typeof WcagScanListQuerySchema>;
