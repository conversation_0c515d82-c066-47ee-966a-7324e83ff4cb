/**
 * Complex UI Component Detector for WCAG Scanning
 * Advanced detection and analysis of modals, dropdowns, carousels, accordions, and custom ARIA widgets
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface UIComponent {
  type: 'modal' | 'dropdown' | 'carousel' | 'accordion' | 'tabs' | 'tooltip' | 'menu' | 'custom-widget';
  selector: string;
  element: string; // tagName
  isVisible: boolean;
  isInteractive: boolean;
  hasProperARIA: boolean;
  hasKeyboardSupport: boolean;
  hasFocusManagement: boolean;
  accessibilityScore: number; // 0-100
  issues: string[];
  recommendations: string[];
  ariaAttributes: Record<string, string>;
  computedRole: string;
  children: UIComponent[];
}

export interface ComponentAnalysisResult {
  components: UIComponent[];
  totalComponents: number;
  accessibleComponents: number;
  criticalIssues: string[];
  recommendations: string[];
  frameworkPatterns: string[];
  customWidgets: UIComponent[];
}

export interface ComponentDetectionConfig {
  enableModalDetection: boolean;
  enableDropdownDetection: boolean;
  enableCarouselDetection: boolean;
  enableAccordionDetection: boolean;
  enableTabDetection: boolean;
  enableTooltipDetection: boolean;
  enableMenuDetection: boolean;
  enableCustomWidgetDetection: boolean;
  testKeyboardInteraction: boolean;
  testFocusManagement: boolean;
  deepAnalysis: boolean;
}

/**
 * Advanced UI component detector with comprehensive accessibility analysis
 */
export class UIComponentDetector {
  private static instance: UIComponentDetector;

  private constructor() {}

  static getInstance(): UIComponentDetector {
    if (!UIComponentDetector.instance) {
      UIComponentDetector.instance = new UIComponentDetector();
    }
    return UIComponentDetector.instance;
  }

  /**
   * Detect and analyze all UI components on the page
   */
  async detectComponents(page: Page, config: Partial<ComponentDetectionConfig> = {}): Promise<ComponentAnalysisResult> {
    const fullConfig: ComponentDetectionConfig = {
      enableModalDetection: config.enableModalDetection ?? true,
      enableDropdownDetection: config.enableDropdownDetection ?? true,
      enableCarouselDetection: config.enableCarouselDetection ?? true,
      enableAccordionDetection: config.enableAccordionDetection ?? true,
      enableTabDetection: config.enableTabDetection ?? true,
      enableTooltipDetection: config.enableTooltipDetection ?? true,
      enableMenuDetection: config.enableMenuDetection ?? true,
      enableCustomWidgetDetection: config.enableCustomWidgetDetection ?? true,
      testKeyboardInteraction: config.testKeyboardInteraction ?? true,
      testFocusManagement: config.testFocusManagement ?? true,
      deepAnalysis: config.deepAnalysis ?? true,
    };

    logger.debug('🔍 Starting complex UI component detection');

    // Inject component detection functions
    await this.injectDetectionFunctions(page);

    // Detect different component types
    const components: UIComponent[] = [];

    if (fullConfig.enableModalDetection) {
      const modals = await this.detectModals(page);
      components.push(...modals);
    }

    if (fullConfig.enableDropdownDetection) {
      const dropdowns = await this.detectDropdowns(page);
      components.push(...dropdowns);
    }

    if (fullConfig.enableCarouselDetection) {
      const carousels = await this.detectCarousels(page);
      components.push(...carousels);
    }

    if (fullConfig.enableAccordionDetection) {
      const accordions = await this.detectAccordions(page);
      components.push(...accordions);
    }

    if (fullConfig.enableTabDetection) {
      const tabs = await this.detectTabs(page);
      components.push(...tabs);
    }

    if (fullConfig.enableTooltipDetection) {
      const tooltips = await this.detectTooltips(page);
      components.push(...tooltips);
    }

    if (fullConfig.enableMenuDetection) {
      const menus = await this.detectMenus(page);
      components.push(...menus);
    }

    if (fullConfig.enableCustomWidgetDetection) {
      const customWidgets = await this.detectCustomWidgets(page);
      components.push(...customWidgets);
    }

    // Perform deep analysis if enabled
    if (fullConfig.deepAnalysis) {
      await this.performDeepAnalysis(page, components, fullConfig);
    }

    // Analyze results
    const result = this.analyzeResults(components);

    logger.info(`✅ UI component detection completed: ${result.totalComponents} components found`, {
      accessible: result.accessibleComponents,
      criticalIssues: result.criticalIssues.length,
      customWidgets: result.customWidgets.length,
    });

    return result;
  }

  /**
   * Inject component detection functions into the page
   */
  private async injectDetectionFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as any).wcagUIComponentDetection = {

        /**
         * Get element selector for identification
         */
        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;

          const path = [];
          let current = element;

          while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
            let selector = current.nodeName.toLowerCase();

            if (current.className) {
              const classes = current.className.split(' ').filter(c => c.trim());
              if (classes.length > 0) {
                selector += '.' + classes.slice(0, 2).join('.');
              }
            }

            path.unshift(selector);
            current = current.parentElement!;

            if (path.length > 4) break; // Limit depth
          }

          return path.join(' > ');
        },

        /**
         * Check if element is visible
         */
        isElementVisible(element: HTMLElement): boolean {
          const style = window.getComputedStyle(element);
          return style.display !== 'none' &&
                 style.visibility !== 'hidden' &&
                 style.opacity !== '0' &&
                 element.offsetWidth > 0 &&
                 element.offsetHeight > 0;
        },

        /**
         * Check if element is interactive
         */
        isElementInteractive(element: HTMLElement): boolean {
          const interactiveTags = ['button', 'input', 'select', 'textarea', 'a'];
          const hasTabIndex = element.hasAttribute('tabindex') && element.getAttribute('tabindex') !== '-1';
          const hasClickHandler = element.onclick !== null;
          const hasRole = element.getAttribute('role');
          const interactiveRoles = ['button', 'link', 'menuitem', 'tab', 'option', 'checkbox', 'radio'];

          return interactiveTags.includes(element.tagName.toLowerCase()) ||
                 hasTabIndex ||
                 hasClickHandler ||
                 (hasRole && interactiveRoles.includes(hasRole));
        },

        /**
         * Get all ARIA attributes
         */
        getAriaAttributes(element: HTMLElement): Record<string, string> {
          const ariaAttrs: Record<string, string> = {};

          for (let i = 0; i < element.attributes.length; i++) {
            const attr = element.attributes[i];
            if (attr.name.startsWith('aria-') || attr.name === 'role') {
              ariaAttrs[attr.name] = attr.value;
            }
          }

          return ariaAttrs;
        },

        /**
         * Get computed role
         */
        getComputedRole(element: HTMLElement): string {
          // Try explicit role first
          const explicitRole = element.getAttribute('role');
          if (explicitRole) return explicitRole;

          // Determine implicit role based on element type
          const tagName = element.tagName.toLowerCase();
          const type = element.getAttribute('type');

          const implicitRoles: Record<string, string> = {
            'button': 'button',
            'a': element.hasAttribute('href') ? 'link' : 'generic',
            'input': type === 'button' || type === 'submit' ? 'button' :
                    type === 'checkbox' ? 'checkbox' :
                    type === 'radio' ? 'radio' : 'textbox',
            'select': 'combobox',
            'textarea': 'textbox',
            'h1': 'heading',
            'h2': 'heading',
            'h3': 'heading',
            'h4': 'heading',
            'h5': 'heading',
            'h6': 'heading',
            'nav': 'navigation',
            'main': 'main',
            'aside': 'complementary',
            'header': 'banner',
            'footer': 'contentinfo',
            'section': 'region',
            'article': 'article',
            'ul': 'list',
            'ol': 'list',
            'li': 'listitem',
            'table': 'table',
            'tr': 'row',
            'td': 'cell',
            'th': 'columnheader',
          };

          return implicitRoles[tagName] || 'generic';
        },

        /**
         * Check ARIA compliance
         */
        checkAriaCompliance(element: HTMLElement, componentType: string): { hasProperARIA: boolean; issues: string[] } {
          const issues: string[] = [];
          const ariaAttrs = this.getAriaAttributes(element);
          const role = this.getComputedRole(element);

          // Component-specific ARIA requirements
          switch (componentType) {
            case 'modal':
              if (!ariaAttrs['aria-modal'] && role !== 'dialog') {
                issues.push('Modal should have aria-modal="true" or role="dialog"');
              }
              if (!ariaAttrs['aria-labelledby'] && !ariaAttrs['aria-label']) {
                issues.push('Modal should have aria-labelledby or aria-label');
              }
              break;

            case 'dropdown':
              if (!ariaAttrs['aria-expanded']) {
                issues.push('Dropdown trigger should have aria-expanded');
              }
              if (!ariaAttrs['aria-haspopup']) {
                issues.push('Dropdown trigger should have aria-haspopup');
              }
              break;

            case 'carousel':
              if (role !== 'region' && !ariaAttrs['aria-label']) {
                issues.push('Carousel should have role="region" and aria-label');
              }
              if (!ariaAttrs['aria-live']) {
                issues.push('Carousel should have aria-live for announcements');
              }
              break;

            case 'accordion':
              if (role !== 'button' && !ariaAttrs['aria-expanded']) {
                issues.push('Accordion header should be a button with aria-expanded');
              }
              if (!ariaAttrs['aria-controls']) {
                issues.push('Accordion header should have aria-controls');
              }
              break;

            case 'tabs':
              if (role !== 'tab' && role !== 'tablist') {
                issues.push('Tab elements should have appropriate tab roles');
              }
              if (role === 'tab' && !ariaAttrs['aria-selected']) {
                issues.push('Tab should have aria-selected');
              }
              break;
          }

          return {
            hasProperARIA: issues.length === 0,
            issues,
          };
        },

        /**
         * Test keyboard support
         */
        testKeyboardSupport(element: HTMLElement): { hasKeyboardSupport: boolean; issues: string[] } {
          const issues: string[] = [];

          // Check if element is focusable
          const tabIndex = element.getAttribute('tabindex');
          const isFocusable = element.matches(':focus-visible, :focus') ||
                             tabIndex !== null && tabIndex !== '-1' ||
                             ['button', 'input', 'select', 'textarea', 'a'].includes(element.tagName.toLowerCase());

          if (!isFocusable) {
            issues.push('Interactive element is not keyboard focusable');
          }

          // Check for keyboard event handlers
          const hasKeyHandlers = element.onkeydown !== null ||
                                element.onkeyup !== null ||
                                element.onkeypress !== null;

          if (this.isElementInteractive(element) && !hasKeyHandlers) {
            issues.push('Interactive element lacks keyboard event handlers');
          }

          return {
            hasKeyboardSupport: issues.length === 0,
            issues,
          };
        },

        /**
         * Calculate accessibility score
         */
        calculateAccessibilityScore(component: any): number {
          let score = 100;

          // Deduct points for missing ARIA
          if (!component.hasProperARIA) {
            score -= 30;
          }

          // Deduct points for keyboard issues
          if (!component.hasKeyboardSupport) {
            score -= 25;
          }

          // Deduct points for focus management issues
          if (!component.hasFocusManagement) {
            score -= 20;
          }

          // Deduct points for visibility issues
          if (!component.isVisible && component.isInteractive) {
            score -= 15;
          }

          // Deduct points for each issue
          score -= Math.min(component.issues.length * 5, 20);

          return Math.max(0, score);
        }
      };
    });
  }

  /**
   * Detect modal components
   */
  private async detectModals(page: Page): Promise<UIComponent[]> {
    return await page.evaluate(() => {
      const modals: UIComponent[] = [];

      // Common modal selectors
      const modalSelectors = [
        '[role="dialog"]',
        '[aria-modal="true"]',
        '.modal',
        '.dialog',
        '.popup',
        '.overlay',
        '[class*="modal"]',
        '[class*="dialog"]',
        '[id*="modal"]',
        '[id*="dialog"]',
      ];

      const foundElements = new Set<HTMLElement>();

      modalSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => foundElements.add(el as HTMLElement));
      });

      foundElements.forEach(element => {
        const detector = (window as any).wcagUIComponentDetection;
        const ariaCheck = detector.checkAriaCompliance(element, 'modal');
        const keyboardCheck = detector.testKeyboardSupport(element);

        const component: UIComponent = {
          type: 'modal',
          selector: detector.getElementSelector(element),
          element: element.tagName.toLowerCase(),
          isVisible: detector.isElementVisible(element),
          isInteractive: detector.isElementInteractive(element),
          hasProperARIA: ariaCheck.hasProperARIA,
          hasKeyboardSupport: keyboardCheck.hasKeyboardSupport,
          hasFocusManagement: this.checkModalFocusManagement(element),
          accessibilityScore: 0, // Will be calculated
          issues: [...ariaCheck.issues, ...keyboardCheck.issues],
          recommendations: [],
          ariaAttributes: detector.getAriaAttributes(element),
          computedRole: detector.getComputedRole(element),
          children: [],
        };

        component.accessibilityScore = detector.calculateAccessibilityScore(component);
        modals.push(component);
      });

      return modals;
    });
  }

  /**
   * Detect dropdown components
   */
  private async detectDropdowns(page: Page): Promise<UIComponent[]> {
    return await page.evaluate(() => {
      const dropdowns: UIComponent[] = [];

      // Common dropdown selectors
      const dropdownSelectors = [
        '[aria-haspopup]',
        '.dropdown',
        '.select',
        '.combobox',
        '[role="combobox"]',
        '[role="listbox"]',
        '[class*="dropdown"]',
        '[class*="select"]',
        'select',
      ];

      const foundElements = new Set<HTMLElement>();

      dropdownSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => foundElements.add(el as HTMLElement));
      });

      foundElements.forEach(element => {
        const detector = (window as any).wcagUIComponentDetection;
        const ariaCheck = detector.checkAriaCompliance(element, 'dropdown');
        const keyboardCheck = detector.testKeyboardSupport(element);

        const component: UIComponent = {
          type: 'dropdown',
          selector: detector.getElementSelector(element),
          element: element.tagName.toLowerCase(),
          isVisible: detector.isElementVisible(element),
          isInteractive: detector.isElementInteractive(element),
          hasProperARIA: ariaCheck.hasProperARIA,
          hasKeyboardSupport: keyboardCheck.hasKeyboardSupport,
          hasFocusManagement: this.checkDropdownFocusManagement(element),
          accessibilityScore: 0,
          issues: [...ariaCheck.issues, ...keyboardCheck.issues],
          recommendations: [],
          ariaAttributes: detector.getAriaAttributes(element),
          computedRole: detector.getComputedRole(element),
          children: [],
        };

        component.accessibilityScore = detector.calculateAccessibilityScore(component);
        dropdowns.push(component);
      });

      return dropdowns;
    });
  }

  /**
   * Detect carousel components
   */
  private async detectCarousels(page: Page): Promise<UIComponent[]> {
    return await page.evaluate(() => {
      const carousels: UIComponent[] = [];

      const carouselSelectors = [
        '[role="region"][aria-label*="carousel"]',
        '.carousel',
        '.slider',
        '.slideshow',
        '[class*="carousel"]',
        '[class*="slider"]',
        '[class*="swiper"]',
        '[id*="carousel"]',
        '[id*="slider"]',
      ];

      const foundElements = new Set<HTMLElement>();

      carouselSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => foundElements.add(el as HTMLElement));
      });

      foundElements.forEach(element => {
        const detector = (window as any).wcagUIComponentDetection;
        const ariaCheck = detector.checkAriaCompliance(element, 'carousel');
        const keyboardCheck = detector.testKeyboardSupport(element);

        const component: UIComponent = {
          type: 'carousel',
          selector: detector.getElementSelector(element),
          element: element.tagName.toLowerCase(),
          isVisible: detector.isElementVisible(element),
          isInteractive: detector.isElementInteractive(element),
          hasProperARIA: ariaCheck.hasProperARIA,
          hasKeyboardSupport: keyboardCheck.hasKeyboardSupport,
          hasFocusManagement: true, // Simplified for now
          accessibilityScore: 0,
          issues: [...ariaCheck.issues, ...keyboardCheck.issues],
          recommendations: [],
          ariaAttributes: detector.getAriaAttributes(element),
          computedRole: detector.getComputedRole(element),
          children: [],
        };

        component.accessibilityScore = detector.calculateAccessibilityScore(component);
        carousels.push(component);
      });

      return carousels;
    });
  }

  /**
   * Detect accordion components
   */
  private async detectAccordions(page: Page): Promise<UIComponent[]> {
    return await page.evaluate(() => {
      const accordions: UIComponent[] = [];

      const accordionSelectors = [
        '.accordion',
        '.collapsible',
        '[class*="accordion"]',
        '[class*="collaps"]',
        '[aria-expanded]',
        'details',
      ];

      const foundElements = new Set<HTMLElement>();

      accordionSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => foundElements.add(el as HTMLElement));
      });

      foundElements.forEach(element => {
        const detector = (window as any).wcagUIComponentDetection;
        const ariaCheck = detector.checkAriaCompliance(element, 'accordion');
        const keyboardCheck = detector.testKeyboardSupport(element);

        const component: UIComponent = {
          type: 'accordion',
          selector: detector.getElementSelector(element),
          element: element.tagName.toLowerCase(),
          isVisible: detector.isElementVisible(element),
          isInteractive: detector.isElementInteractive(element),
          hasProperARIA: ariaCheck.hasProperARIA,
          hasKeyboardSupport: keyboardCheck.hasKeyboardSupport,
          hasFocusManagement: true,
          accessibilityScore: 0,
          issues: [...ariaCheck.issues, ...keyboardCheck.issues],
          recommendations: [],
          ariaAttributes: detector.getAriaAttributes(element),
          computedRole: detector.getComputedRole(element),
          children: [],
        };

        component.accessibilityScore = detector.calculateAccessibilityScore(component);
        accordions.push(component);
      });

      return accordions;
    });
  }

  /**
   * Detect tab components
   */
  private async detectTabs(page: Page): Promise<UIComponent[]> {
    return await page.evaluate(() => {
      const tabs: UIComponent[] = [];

      const tabSelectors = [
        '[role="tablist"]',
        '[role="tab"]',
        '.tabs',
        '.tab-list',
        '[class*="tab"]',
      ];

      const foundElements = new Set<HTMLElement>();

      tabSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => foundElements.add(el as HTMLElement));
      });

      foundElements.forEach(element => {
        const detector = (window as any).wcagUIComponentDetection;
        const ariaCheck = detector.checkAriaCompliance(element, 'tabs');
        const keyboardCheck = detector.testKeyboardSupport(element);

        const component: UIComponent = {
          type: 'tabs',
          selector: detector.getElementSelector(element),
          element: element.tagName.toLowerCase(),
          isVisible: detector.isElementVisible(element),
          isInteractive: detector.isElementInteractive(element),
          hasProperARIA: ariaCheck.hasProperARIA,
          hasKeyboardSupport: keyboardCheck.hasKeyboardSupport,
          hasFocusManagement: true,
          accessibilityScore: 0,
          issues: [...ariaCheck.issues, ...keyboardCheck.issues],
          recommendations: [],
          ariaAttributes: detector.getAriaAttributes(element),
          computedRole: detector.getComputedRole(element),
          children: [],
        };

        component.accessibilityScore = detector.calculateAccessibilityScore(component);
        tabs.push(component);
      });

      return tabs;
    });
  }

  /**
   * Detect tooltip components
   */
  private async detectTooltips(page: Page): Promise<UIComponent[]> {
    return await page.evaluate(() => {
      const tooltips: UIComponent[] = [];

      const tooltipSelectors = [
        '[role="tooltip"]',
        '.tooltip',
        '[class*="tooltip"]',
        '[aria-describedby]',
        '[title]',
      ];

      const foundElements = new Set<HTMLElement>();

      tooltipSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => foundElements.add(el as HTMLElement));
      });

      foundElements.forEach(element => {
        const detector = (window as any).wcagUIComponentDetection;
        const ariaCheck = detector.checkAriaCompliance(element, 'tooltip');
        const keyboardCheck = detector.testKeyboardSupport(element);

        const component: UIComponent = {
          type: 'tooltip',
          selector: detector.getElementSelector(element),
          element: element.tagName.toLowerCase(),
          isVisible: detector.isElementVisible(element),
          isInteractive: detector.isElementInteractive(element),
          hasProperARIA: ariaCheck.hasProperARIA,
          hasKeyboardSupport: keyboardCheck.hasKeyboardSupport,
          hasFocusManagement: true,
          accessibilityScore: 0,
          issues: [...ariaCheck.issues, ...keyboardCheck.issues],
          recommendations: [],
          ariaAttributes: detector.getAriaAttributes(element),
          computedRole: detector.getComputedRole(element),
          children: [],
        };

        component.accessibilityScore = detector.calculateAccessibilityScore(component);
        tooltips.push(component);
      });

      return tooltips;
    });
  }

  /**
   * Detect menu components
   */
  private async detectMenus(page: Page): Promise<UIComponent[]> {
    return await page.evaluate(() => {
      const menus: UIComponent[] = [];

      const menuSelectors = [
        '[role="menu"]',
        '[role="menubar"]',
        '[role="menuitem"]',
        'nav',
        '.menu',
        '.navigation',
        '[class*="menu"]',
        '[class*="nav"]',
      ];

      const foundElements = new Set<HTMLElement>();

      menuSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => foundElements.add(el as HTMLElement));
      });

      foundElements.forEach(element => {
        const detector = (window as any).wcagUIComponentDetection;
        const ariaCheck = detector.checkAriaCompliance(element, 'menu');
        const keyboardCheck = detector.testKeyboardSupport(element);

        const component: UIComponent = {
          type: 'menu',
          selector: detector.getElementSelector(element),
          element: element.tagName.toLowerCase(),
          isVisible: detector.isElementVisible(element),
          isInteractive: detector.isElementInteractive(element),
          hasProperARIA: ariaCheck.hasProperARIA,
          hasKeyboardSupport: keyboardCheck.hasKeyboardSupport,
          hasFocusManagement: true,
          accessibilityScore: 0,
          issues: [...ariaCheck.issues, ...keyboardCheck.issues],
          recommendations: [],
          ariaAttributes: detector.getAriaAttributes(element),
          computedRole: detector.getComputedRole(element),
          children: [],
        };

        component.accessibilityScore = detector.calculateAccessibilityScore(component);
        menus.push(component);
      });

      return menus;
    });
  }

  /**
   * Detect custom widgets
   */
  private async detectCustomWidgets(page: Page): Promise<UIComponent[]> {
    return await page.evaluate(() => {
      const customWidgets: UIComponent[] = [];

      // Look for elements with custom ARIA roles or complex interactions
      const customSelectors = [
        '[role]:not([role="button"]):not([role="link"]):not([role="textbox"])',
        '[aria-live]',
        '[aria-atomic]',
        '[aria-relevant]',
        '[class*="widget"]',
        '[class*="component"]',
        '[data-widget]',
        '[data-component]',
      ];

      const foundElements = new Set<HTMLElement>();

      customSelectors.forEach(selector => {
        const elements = document.querySelectorAll(selector);
        elements.forEach(el => foundElements.add(el as HTMLElement));
      });

      foundElements.forEach(element => {
        const detector = (window as any).wcagUIComponentDetection;
        const ariaAttrs = detector.getAriaAttributes(element);

        // Only include if it has significant ARIA attributes or custom behavior
        if (Object.keys(ariaAttrs).length > 1 || detector.isElementInteractive(element)) {
          const ariaCheck = detector.checkAriaCompliance(element, 'custom-widget');
          const keyboardCheck = detector.testKeyboardSupport(element);

          const component: UIComponent = {
            type: 'custom-widget',
            selector: detector.getElementSelector(element),
            element: element.tagName.toLowerCase(),
            isVisible: detector.isElementVisible(element),
            isInteractive: detector.isElementInteractive(element),
            hasProperARIA: ariaCheck.hasProperARIA,
            hasKeyboardSupport: keyboardCheck.hasKeyboardSupport,
            hasFocusManagement: true,
            accessibilityScore: 0,
            issues: [...ariaCheck.issues, ...keyboardCheck.issues],
            recommendations: [],
            ariaAttributes: ariaAttrs,
            computedRole: detector.getComputedRole(element),
            children: [],
          };

          component.accessibilityScore = detector.calculateAccessibilityScore(component);
          customWidgets.push(component);
        }
      });

      return customWidgets;
    });
  }

  /**
   * Perform deep analysis on detected components
   */
  private async performDeepAnalysis(
    page: Page,
    components: UIComponent[],
    config: ComponentDetectionConfig
  ): Promise<void> {
    // Add deep analysis logic here
    // This could include testing actual keyboard interactions, focus trapping, etc.
    logger.debug(`🔬 Performing deep analysis on ${components.length} components`);
  }

  /**
   * Analyze detection results
   */
  private analyzeResults(components: UIComponent[]): ComponentAnalysisResult {
    const totalComponents = components.length;
    const accessibleComponents = components.filter(c => c.accessibilityScore >= 80).length;
    const criticalIssues: string[] = [];
    const recommendations: string[] = [];
    const customWidgets = components.filter(c => c.type === 'custom-widget');

    // Collect critical issues
    components.forEach(component => {
      if (component.accessibilityScore < 50) {
        criticalIssues.push(`${component.type} at ${component.selector} has critical accessibility issues`);
      }

      if (!component.hasProperARIA) {
        criticalIssues.push(`${component.type} at ${component.selector} lacks proper ARIA attributes`);
      }

      if (!component.hasKeyboardSupport) {
        criticalIssues.push(`${component.type} at ${component.selector} lacks keyboard support`);
      }
    });

    // Generate recommendations
    const componentTypes = [...new Set(components.map(c => c.type))];
    componentTypes.forEach(type => {
      const typeComponents = components.filter(c => c.type === type);
      const accessibleCount = typeComponents.filter(c => c.accessibilityScore >= 80).length;

      if (accessibleCount < typeComponents.length) {
        recommendations.push(`Improve accessibility for ${type} components (${accessibleCount}/${typeComponents.length} accessible)`);
      }
    });

    // Detect framework patterns
    const frameworkPatterns: string[] = [];
    if (components.some(c => c.selector.includes('react'))) {
      frameworkPatterns.push('React');
    }
    if (components.some(c => c.selector.includes('vue'))) {
      frameworkPatterns.push('Vue');
    }
    if (components.some(c => c.selector.includes('angular'))) {
      frameworkPatterns.push('Angular');
    }

    return {
      components,
      totalComponents,
      accessibleComponents,
      criticalIssues,
      recommendations,
      frameworkPatterns,
      customWidgets,
    };
  }
}

export default UIComponentDetector;