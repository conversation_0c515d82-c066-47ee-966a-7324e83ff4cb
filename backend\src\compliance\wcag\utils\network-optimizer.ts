/**
 * Network Optimizer for VPS Environments
 * Network bandwidth optimization and connection pooling for VPS environments
 */

import * as http from 'http';
import * as https from 'https';
import { URL } from 'url';
import logger from '../../../utils/logger';

export interface NetworkStats {
  activeConnections: number;
  totalRequests: number;
  successfulRequests: number;
  failedRequests: number;
  averageResponseTime: number;
  bandwidth: {
    incoming: number; // bytes/sec
    outgoing: number; // bytes/sec
    total: number; // bytes/sec
  };
  connectionPool: {
    http: number;
    https: number;
    total: number;
    maxConnections: number;
  };
}

export interface ConnectionPoolConfig {
  maxSockets: number;
  maxFreeSockets: number;
  timeout: number;
  keepAlive: boolean;
  keepAliveMsecs: number;
  maxCachedSessions: number;
}

export interface NetworkOptimizationConfig {
  enableConnectionPooling: boolean;
  enableRequestCompression: boolean;
  enableResponseCaching: boolean;
  enableBandwidthThrottling: boolean;
  maxConcurrentRequests: number;
  requestTimeout: number;
  retryAttempts: number;
  retryDelay: number;
  connectionPoolConfig: ConnectionPoolConfig;
}

export interface RequestMetrics {
  url: string;
  method: string;
  startTime: number;
  endTime?: number;
  responseTime?: number;
  statusCode?: number;
  bytes: {
    sent: number;
    received: number;
  };
  error?: Error;
  retries: number;
}

/**
 * Advanced network optimizer for VPS environments
 */
export class NetworkOptimizer {
  private static instance: NetworkOptimizer;
  private httpAgent: http.Agent;
  private httpsAgent: https.Agent;
  private requestMetrics: RequestMetrics[] = [];
  private activeRequests: Map<string, RequestMetrics> = new Map();
  private config: NetworkOptimizationConfig;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private bandwidthHistory: number[] = [];
  private connectionHistory: number[] = [];

  private constructor() {
    this.config = this.getDefaultConfig();
    this.setupConnectionPools();
    this.startNetworkMonitoring();
  }

  static getInstance(): NetworkOptimizer {
    if (!NetworkOptimizer.instance) {
      NetworkOptimizer.instance = new NetworkOptimizer();
    }
    return NetworkOptimizer.instance;
  }

  /**
   * Get current network statistics
   */
  getCurrentNetworkStats(): NetworkStats {
    const now = Date.now();
    const recentMetrics = this.requestMetrics.filter(m => 
      m.endTime && now - m.endTime < 300000 // Last 5 minutes
    );

    const totalRequests = recentMetrics.length;
    const successfulRequests = recentMetrics.filter(m => 
      m.statusCode && m.statusCode >= 200 && m.statusCode < 400
    ).length;
    const failedRequests = totalRequests - successfulRequests;

    const averageResponseTime = totalRequests > 0
      ? recentMetrics.reduce((sum, m) => sum + (m.responseTime || 0), 0) / totalRequests
      : 0;

    // Calculate bandwidth (simplified)
    const totalBytes = recentMetrics.reduce((sum, m) => 
      sum + m.bytes.sent + m.bytes.received, 0
    );
    const timeSpanSeconds = 300; // 5 minutes
    const bandwidth = totalBytes / timeSpanSeconds;

    return {
      activeConnections: this.activeRequests.size,
      totalRequests,
      successfulRequests,
      failedRequests,
      averageResponseTime,
      bandwidth: {
        incoming: bandwidth * 0.8, // Estimate
        outgoing: bandwidth * 0.2, // Estimate
        total: bandwidth,
      },
      connectionPool: {
        http: this.httpAgent.getCurrentConnections?.() || 0,
        https: this.httpsAgent.getCurrentConnections?.() || 0,
        total: (this.httpAgent.getCurrentConnections?.() || 0) + 
               (this.httpsAgent.getCurrentConnections?.() || 0),
        maxConnections: this.config.connectionPoolConfig.maxSockets,
      },
    };
  }

  /**
   * Optimize network request
   */
  async optimizeRequest(url: string, options: any = {}): Promise<any> {
    const requestId = `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    const parsedUrl = new URL(url);
    
    const metrics: RequestMetrics = {
      url,
      method: options.method || 'GET',
      startTime: Date.now(),
      bytes: { sent: 0, received: 0 },
      retries: 0,
    };

    this.activeRequests.set(requestId, metrics);

    try {
      // Select appropriate agent
      const agent = parsedUrl.protocol === 'https:' ? this.httpsAgent : this.httpAgent;
      
      // Add optimization options
      const optimizedOptions = {
        ...options,
        agent,
        timeout: this.config.requestTimeout,
        headers: {
          ...options.headers,
          'Connection': 'keep-alive',
          'Accept-Encoding': this.config.enableRequestCompression ? 'gzip, deflate' : undefined,
        },
      };

      // Execute request with retries
      const result = await this.executeRequestWithRetries(url, optimizedOptions, metrics);
      
      metrics.endTime = Date.now();
      metrics.responseTime = metrics.endTime - metrics.startTime;
      
      // Store metrics
      this.requestMetrics.push(metrics);
      
      // Clean up old metrics (keep last 1000)
      if (this.requestMetrics.length > 1000) {
        this.requestMetrics = this.requestMetrics.slice(-1000);
      }

      logger.debug(`Network request optimized: ${url}`, {
        responseTime: metrics.responseTime,
        statusCode: metrics.statusCode,
        bytes: metrics.bytes,
        retries: metrics.retries,
      });

      return result;

    } catch (error) {
      metrics.error = error as Error;
      metrics.endTime = Date.now();
      metrics.responseTime = metrics.endTime - metrics.startTime;
      
      this.requestMetrics.push(metrics);
      
      logger.error(`Network request failed: ${url}`, {
        error: error.message,
        responseTime: metrics.responseTime,
        retries: metrics.retries,
      });

      throw error;

    } finally {
      this.activeRequests.delete(requestId);
    }
  }

  /**
   * Get network optimization recommendations
   */
  getNetworkOptimizationRecommendations(): string[] {
    const stats = this.getCurrentNetworkStats();
    const recommendations: string[] = [];

    // High failure rate
    const failureRate = stats.totalRequests > 0 
      ? (stats.failedRequests / stats.totalRequests) * 100 
      : 0;

    if (failureRate > 10) {
      recommendations.push(`High request failure rate (${failureRate.toFixed(1)}%) - check network connectivity`);
    }

    // Slow response times
    if (stats.averageResponseTime > 5000) {
      recommendations.push(`Slow average response time (${stats.averageResponseTime.toFixed(0)}ms) - consider optimizing requests`);
    }

    // High connection usage
    const connectionUsage = stats.connectionPool.maxConnections > 0
      ? (stats.connectionPool.total / stats.connectionPool.maxConnections) * 100
      : 0;

    if (connectionUsage > 80) {
      recommendations.push(`High connection pool usage (${connectionUsage.toFixed(1)}%) - consider increasing pool size`);
    }

    // Bandwidth optimization
    if (stats.bandwidth.total > 10 * 1024 * 1024) { // 10 MB/s
      recommendations.push('High bandwidth usage - consider implementing request compression');
    }

    // Connection pooling
    if (!this.config.enableConnectionPooling) {
      recommendations.push('Connection pooling disabled - enable for better performance');
    }

    // Request compression
    if (!this.config.enableRequestCompression) {
      recommendations.push('Request compression disabled - enable to reduce bandwidth usage');
    }

    return recommendations;
  }

  /**
   * Get network health score (0-100)
   */
  getNetworkHealthScore(): number {
    const stats = this.getCurrentNetworkStats();
    let score = 100;

    // Deduct for high failure rate
    const failureRate = stats.totalRequests > 0 
      ? (stats.failedRequests / stats.totalRequests) * 100 
      : 0;

    if (failureRate > 20) {
      score -= 40;
    } else if (failureRate > 10) {
      score -= 25;
    } else if (failureRate > 5) {
      score -= 15;
    }

    // Deduct for slow response times
    if (stats.averageResponseTime > 10000) {
      score -= 30;
    } else if (stats.averageResponseTime > 5000) {
      score -= 20;
    } else if (stats.averageResponseTime > 3000) {
      score -= 10;
    }

    // Deduct for high connection usage
    const connectionUsage = stats.connectionPool.maxConnections > 0
      ? (stats.connectionPool.total / stats.connectionPool.maxConnections) * 100
      : 0;

    if (connectionUsage > 95) {
      score -= 25;
    } else if (connectionUsage > 85) {
      score -= 15;
    } else if (connectionUsage > 75) {
      score -= 10;
    }

    // Deduct for many active requests
    if (stats.activeConnections > this.config.maxConcurrentRequests * 0.9) {
      score -= 20;
    }

    return Math.max(0, score);
  }

  /**
   * Configure network optimization settings
   */
  configure(config: Partial<NetworkOptimizationConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Recreate connection pools with new config
    this.setupConnectionPools();
    
    logger.info('Network optimizer configuration updated', { config: this.config });
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): NetworkOptimizationConfig {
    return {
      enableConnectionPooling: process.env.WCAG_ENABLE_CONNECTION_POOLING !== 'false',
      enableRequestCompression: process.env.WCAG_ENABLE_REQUEST_COMPRESSION !== 'false',
      enableResponseCaching: process.env.WCAG_ENABLE_RESPONSE_CACHING !== 'false',
      enableBandwidthThrottling: process.env.WCAG_ENABLE_BANDWIDTH_THROTTLING === 'true',
      maxConcurrentRequests: parseInt(process.env.WCAG_MAX_CONCURRENT_REQUESTS || '20'),
      requestTimeout: parseInt(process.env.WCAG_REQUEST_TIMEOUT || '30000'), // 30 seconds
      retryAttempts: parseInt(process.env.WCAG_RETRY_ATTEMPTS || '3'),
      retryDelay: parseInt(process.env.WCAG_RETRY_DELAY || '1000'), // 1 second
      connectionPoolConfig: {
        maxSockets: parseInt(process.env.WCAG_MAX_SOCKETS || '50'),
        maxFreeSockets: parseInt(process.env.WCAG_MAX_FREE_SOCKETS || '10'),
        timeout: parseInt(process.env.WCAG_SOCKET_TIMEOUT || '60000'), // 1 minute
        keepAlive: process.env.WCAG_KEEP_ALIVE !== 'false',
        keepAliveMsecs: parseInt(process.env.WCAG_KEEP_ALIVE_MSECS || '1000'),
        maxCachedSessions: parseInt(process.env.WCAG_MAX_CACHED_SESSIONS || '100'),
      },
    };
  }

  /**
   * Setup connection pools
   */
  private setupConnectionPools(): void {
    const poolConfig = this.config.connectionPoolConfig;

    // HTTP Agent
    this.httpAgent = new http.Agent({
      maxSockets: poolConfig.maxSockets,
      maxFreeSockets: poolConfig.maxFreeSockets,
      timeout: poolConfig.timeout,
      keepAlive: poolConfig.keepAlive,
      keepAliveMsecs: poolConfig.keepAliveMsecs,
    });

    // HTTPS Agent
    this.httpsAgent = new https.Agent({
      maxSockets: poolConfig.maxSockets,
      maxFreeSockets: poolConfig.maxFreeSockets,
      timeout: poolConfig.timeout,
      keepAlive: poolConfig.keepAlive,
      keepAliveMsecs: poolConfig.keepAliveMsecs,
      maxCachedSessions: poolConfig.maxCachedSessions,
    });

    logger.debug('Connection pools configured', {
      maxSockets: poolConfig.maxSockets,
      keepAlive: poolConfig.keepAlive,
    });
  }

  /**
   * Execute request with retries
   */
  private async executeRequestWithRetries(
    url: string, 
    options: any, 
    metrics: RequestMetrics
  ): Promise<any> {
    let lastError: Error | null = null;

    for (let attempt = 0; attempt <= this.config.retryAttempts; attempt++) {
      try {
        if (attempt > 0) {
          metrics.retries = attempt;
          await this.delay(this.config.retryDelay * attempt);
        }

        const result = await this.executeRequest(url, options, metrics);
        return result;

      } catch (error) {
        lastError = error as Error;
        
        // Don't retry on certain errors
        if (this.shouldNotRetry(error as Error)) {
          break;
        }

        logger.debug(`Request attempt ${attempt + 1} failed: ${url}`, {
          error: error.message,
          willRetry: attempt < this.config.retryAttempts,
        });
      }
    }

    throw lastError;
  }

  /**
   * Execute a single request
   */
  private async executeRequest(url: string, options: any, metrics: RequestMetrics): Promise<any> {
    return new Promise((resolve, reject) => {
      const parsedUrl = new URL(url);
      const requestModule = parsedUrl.protocol === 'https:' ? https : http;

      const req = requestModule.request(url, options, (res) => {
        metrics.statusCode = res.statusCode;
        
        let data = '';
        let bytesReceived = 0;

        res.on('data', (chunk) => {
          data += chunk;
          bytesReceived += chunk.length;
        });

        res.on('end', () => {
          metrics.bytes.received = bytesReceived;
          
          if (res.statusCode && res.statusCode >= 200 && res.statusCode < 400) {
            resolve({
              statusCode: res.statusCode,
              headers: res.headers,
              data,
              bytes: bytesReceived,
            });
          } else {
            reject(new Error(`HTTP ${res.statusCode}: ${res.statusMessage}`));
          }
        });
      });

      req.on('error', (error) => {
        reject(error);
      });

      req.on('timeout', () => {
        req.destroy();
        reject(new Error('Request timeout'));
      });

      // Track bytes sent
      if (options.body) {
        metrics.bytes.sent = Buffer.byteLength(options.body);
        req.write(options.body);
      }

      req.end();
    });
  }

  /**
   * Check if error should not be retried
   */
  private shouldNotRetry(error: Error): boolean {
    const noRetryErrors = [
      'ENOTFOUND', // DNS resolution failed
      'ECONNREFUSED', // Connection refused
      'CERT_', // Certificate errors
    ];

    return noRetryErrors.some(errorType => 
      error.message.includes(errorType) || error.code?.includes(errorType)
    );
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms));
  }

  /**
   * Start network monitoring
   */
  private startNetworkMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(() => {
      try {
        const stats = this.getCurrentNetworkStats();
        
        // Store bandwidth history
        this.bandwidthHistory.push(stats.bandwidth.total);
        if (this.bandwidthHistory.length > 60) { // Keep last hour
          this.bandwidthHistory.shift();
        }

        // Store connection history
        this.connectionHistory.push(stats.connectionPool.total);
        if (this.connectionHistory.length > 60) {
          this.connectionHistory.shift();
        }

        // Log warnings for high usage
        if (stats.connectionPool.total > stats.connectionPool.maxConnections * 0.9) {
          logger.warn('High connection pool usage', {
            current: stats.connectionPool.total,
            max: stats.connectionPool.maxConnections,
          });
        }

        if (stats.averageResponseTime > 5000) {
          logger.warn('Slow network response times', {
            averageMs: stats.averageResponseTime,
          });
        }

      } catch (error) {
        logger.error('Error during network monitoring', { error });
      }
    }, 60000); // Monitor every minute
  }

  /**
   * Stop network monitoring
   */
  stopMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    // Destroy agents
    this.httpAgent.destroy();
    this.httpsAgent.destroy();
  }

  /**
   * Get comprehensive network report
   */
  getNetworkReport(): {
    currentStats: NetworkStats;
    healthScore: number;
    recommendations: string[];
    recentMetrics: RequestMetrics[];
    bandwidthHistory: number[];
    connectionHistory: number[];
    config: NetworkOptimizationConfig;
  } {
    const now = Date.now();
    const recentMetrics = this.requestMetrics.filter(m => 
      m.endTime && now - m.endTime < 300000 // Last 5 minutes
    );

    return {
      currentStats: this.getCurrentNetworkStats(),
      healthScore: this.getNetworkHealthScore(),
      recommendations: this.getNetworkOptimizationRecommendations(),
      recentMetrics,
      bandwidthHistory: this.bandwidthHistory.slice(),
      connectionHistory: this.connectionHistory.slice(),
      config: this.config,
    };
  }
}

export default NetworkOptimizer;
