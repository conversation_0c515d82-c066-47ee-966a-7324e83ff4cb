/**
 * Enhanced WCAG Scan Configuration Component
 * Advanced configuration options for Phase 1-3 enhanced scanning
 */

import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  TextField,
  FormControl,
  FormLabel,
  FormGroup,
  FormControlLabel,
  Checkbox,
  RadioGroup,
  Radio,
  Select,
  MenuItem,
  InputLabel,
  Slider,
  Switch,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Button,
  Grid,
  Chip,
  Alert,
  Tooltip,
  IconButton,
  Divider,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Info as InfoIcon,
  Speed as SpeedIcon,
  Psychology as AIIcon,
  Memory as MemoryIcon,
  Settings as SettingsIcon,
  Security as SecurityIcon,
} from '@mui/icons-material';

interface ScanConfiguration {
  // Basic Configuration
  targetUrl: string;
  wcagVersion: '2.1' | '2.2';
  level: 'A' | 'AA' | 'AAA';
  timeout: number;
  
  // Phase 1: Performance Options
  performance: {
    enableBrowserPool: boolean;
    enableSmartCache: boolean;
    enablePerformanceMonitoring: boolean;
    maxConcurrentScans: number;
    cacheExpiry: number;
    memoryThreshold: number;
  };
  
  // Phase 2: Website Analysis
  websiteAnalysis: {
    enableCMSDetection: boolean;
    enableEcommerceAnalysis: boolean;
    enableFrameworkDetection: boolean;
    enableMediaAnalysis: boolean;
    deepAnalysis: boolean;
    platformSpecificOptimizations: boolean;
  };
  
  // Phase 3: VPS Optimization
  vpsOptimization: {
    enableResourceManagement: boolean;
    enableMemoryOptimization: boolean;
    enableCPUOptimization: boolean;
    enableNetworkOptimization: boolean;
    enableStorageOptimization: boolean;
    enableAutoOptimization: boolean;
    vpsProfile: 'auto' | 'micro' | 'small' | 'medium' | 'large' | 'xlarge';
  };
  
  // Advanced Options
  advanced: {
    includeHiddenElements: boolean;
    enableManualReview: boolean;
    enableDetailedReporting: boolean;
    enableScreenshots: boolean;
    customRules: string[];
    excludeRules: string[];
  };
  
  // Authentication & Security
  authentication: {
    requiresAuth: boolean;
    authType: 'none' | 'basic' | 'form' | 'oauth';
    credentials?: {
      username: string;
      password: string;
    };
    headers: { [key: string]: string };
  };
}

interface EnhancedScanConfigurationProps {
  initialConfig?: Partial<ScanConfiguration>;
  onConfigChange: (config: ScanConfiguration) => void;
  onStartScan: (config: ScanConfiguration) => void;
  isScanning?: boolean;
}

export const EnhancedScanConfiguration: React.FC<EnhancedScanConfigurationProps> = ({
  initialConfig,
  onConfigChange,
  onStartScan,
  isScanning = false,
}) => {
  const [config, setConfig] = useState<ScanConfiguration>({
    targetUrl: '',
    wcagVersion: '2.2',
    level: 'AA',
    timeout: 120000,
    
    performance: {
      enableBrowserPool: true,
      enableSmartCache: true,
      enablePerformanceMonitoring: true,
      maxConcurrentScans: 4,
      cacheExpiry: 60,
      memoryThreshold: 2048,
    },
    
    websiteAnalysis: {
      enableCMSDetection: true,
      enableEcommerceAnalysis: true,
      enableFrameworkDetection: true,
      enableMediaAnalysis: true,
      deepAnalysis: true,
      platformSpecificOptimizations: true,
    },
    
    vpsOptimization: {
      enableResourceManagement: true,
      enableMemoryOptimization: true,
      enableCPUOptimization: true,
      enableNetworkOptimization: true,
      enableStorageOptimization: true,
      enableAutoOptimization: true,
      vpsProfile: 'auto',
    },
    
    advanced: {
      includeHiddenElements: false,
      enableManualReview: true,
      enableDetailedReporting: true,
      enableScreenshots: false,
      customRules: [],
      excludeRules: [],
    },
    
    authentication: {
      requiresAuth: false,
      authType: 'none',
      headers: {},
    },
    
    ...initialConfig,
  });

  const [expandedSection, setExpandedSection] = useState<string>('basic');

  useEffect(() => {
    onConfigChange(config);
  }, [config, onConfigChange]);

  const handleConfigChange = (section: keyof ScanConfiguration, field: string, value: any) => {
    setConfig(prev => ({
      ...prev,
      [section]: {
        ...((prev[section] as Record<string, unknown>) || {}),
        [field]: value,
      },
    }));
  };

  const handleBasicChange = (field: keyof ScanConfiguration, value: any) => {
    setConfig(prev => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleAccordionChange = (panel: string) => (
    event: React.SyntheticEvent,
    isExpanded: boolean
  ) => {
    setExpandedSection(isExpanded ? panel : '');
  };

  const renderBasicConfiguration = () => (
    <div style={{ display: 'grid', gap: '24px', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))' }}>
      <div style={{ gridColumn: '1 / -1' }}>
        <TextField
          fullWidth
          label="Target URL"
          value={config.targetUrl}
          onChange={(e) => handleBasicChange('targetUrl', e.target.value)}
          placeholder="https://example.com"
          required
        />
      </div>

      <div>
        <FormControl fullWidth>
          <InputLabel>WCAG Version</InputLabel>
          <Select
            value={config.wcagVersion}
            onChange={(e) => handleBasicChange('wcagVersion', e.target.value)}
          >
            <MenuItem value="2.1">WCAG 2.1</MenuItem>
            <MenuItem value="2.2">WCAG 2.2 (Recommended)</MenuItem>
          </Select>
        </FormControl>
      </div>

      <div>
        <FormControl fullWidth>
          <InputLabel>Compliance Level</InputLabel>
          <Select
            value={config.level}
            onChange={(e) => handleBasicChange('level', e.target.value)}
          >
            <MenuItem value="A">Level A</MenuItem>
            <MenuItem value="AA">Level AA (Recommended)</MenuItem>
            <MenuItem value="AAA">Level AAA</MenuItem>
          </Select>
        </FormControl>
      </div>

      <div>
        <TextField
          fullWidth
          label="Timeout (seconds)"
          type="number"
          value={config.timeout / 1000}
          onChange={(e) => handleBasicChange('timeout', parseInt(e.target.value) * 1000)}
          inputProps={{ min: 30, max: 300 }}
        />
      </div>
    </div>
  );

  const renderPerformanceConfiguration = () => (
    <div style={{ display: 'flex', flexDirection: 'column', gap: '24px' }}>
      <div>
        <Alert severity="info" icon={<SpeedIcon />}>
          Phase 1 Performance Enhancements - Optimize scan speed and resource usage
        </Alert>
      </div>

      <div style={{ display: 'grid', gap: '24px', gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))' }}>
        <div>
        <FormGroup>
          <FormControlLabel
            control={
              <Switch
                checked={config.performance.enableBrowserPool}
                onChange={(e) => handleConfigChange('performance', 'enableBrowserPool', e.target.checked)}
              />
            }
            label="Enable Browser Pool"
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.performance.enableSmartCache}
                onChange={(e) => handleConfigChange('performance', 'enableSmartCache', e.target.checked)}
              />
            }
            label="Enable Smart Caching"
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.performance.enablePerformanceMonitoring}
                onChange={(e) => handleConfigChange('performance', 'enablePerformanceMonitoring', e.target.checked)}
              />
            }
            label="Enable Performance Monitoring"
          />
        </FormGroup>
        </div>

        <div>
          <Box mb={3}>
            <Typography gutterBottom>Max Concurrent Scans</Typography>
            <Slider
              value={config.performance.maxConcurrentScans}
              onChange={(e, value) => handleConfigChange('performance', 'maxConcurrentScans', value)}
              min={1}
              max={8}
              marks
              valueLabelDisplay="auto"
            />
          </Box>

          <Box mb={3}>
            <Typography gutterBottom>Cache Expiry (minutes)</Typography>
            <Slider
              value={config.performance.cacheExpiry}
              onChange={(e, value) => handleConfigChange('performance', 'cacheExpiry', value)}
              min={5}
              max={120}
              valueLabelDisplay="auto"
            />
          </Box>

          <Box>
            <Typography gutterBottom>Memory Threshold (MB)</Typography>
            <Slider
              value={config.performance.memoryThreshold}
              onChange={(e, value) => handleConfigChange('performance', 'memoryThreshold', value)}
              min={512}
              max={4096}
              step={256}
              valueLabelDisplay="auto"
            />
          </Box>
        </div>
      </div>
    </div>
  );

  const renderWebsiteAnalysisConfiguration = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Alert severity="info" icon={<AIIcon />}>
          Phase 2 Website Analysis - Intelligent platform detection and optimization
        </Alert>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormGroup>
          <FormControlLabel
            control={
              <Switch
                checked={config.websiteAnalysis.enableCMSDetection}
                onChange={(e) => handleConfigChange('websiteAnalysis', 'enableCMSDetection', e.target.checked)}
              />
            }
            label={
              <Box display="flex" alignItems="center">
                CMS Detection
                <Tooltip title="Detect WordPress, Drupal, Shopify, and other CMS platforms">
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.websiteAnalysis.enableEcommerceAnalysis}
                onChange={(e) => handleConfigChange('websiteAnalysis', 'enableEcommerceAnalysis', e.target.checked)}
              />
            }
            label={
              <Box display="flex" alignItems="center">
                E-commerce Analysis
                <Tooltip title="Analyze shopping carts, checkout flows, and payment forms">
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.websiteAnalysis.enableFrameworkDetection}
                onChange={(e) => handleConfigChange('websiteAnalysis', 'enableFrameworkDetection', e.target.checked)}
              />
            }
            label={
              <Box display="flex" alignItems="center">
                Framework Detection
                <Tooltip title="Detect React, Vue, Angular, and other frameworks">
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            }
          />
        </FormGroup>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormGroup>
          <FormControlLabel
            control={
              <Switch
                checked={config.websiteAnalysis.enableMediaAnalysis}
                onChange={(e) => handleConfigChange('websiteAnalysis', 'enableMediaAnalysis', e.target.checked)}
              />
            }
            label={
              <Box display="flex" alignItems="center">
                Media Analysis
                <Tooltip title="Analyze video, audio, and multimedia content">
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.websiteAnalysis.deepAnalysis}
                onChange={(e) => handleConfigChange('websiteAnalysis', 'deepAnalysis', e.target.checked)}
              />
            }
            label={
              <Box display="flex" alignItems="center">
                Deep Analysis
                <Tooltip title="Perform comprehensive analysis with detailed insights">
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            }
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.websiteAnalysis.platformSpecificOptimizations}
                onChange={(e) => handleConfigChange('websiteAnalysis', 'platformSpecificOptimizations', e.target.checked)}
              />
            }
            label={
              <Box display="flex" alignItems="center">
                Platform Optimizations
                <Tooltip title="Apply platform-specific accessibility optimizations">
                  <IconButton size="small">
                    <InfoIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            }
          />
        </FormGroup>
      </Grid>
    </Grid>
  );

  const renderVPSOptimizationConfiguration = () => (
    <Grid container spacing={3}>
      <Grid item xs={12}>
        <Alert severity="info" icon={<MemoryIcon />}>
          Phase 3 VPS Optimization - Optimize performance for VPS environments
        </Alert>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth mb={3}>
          <InputLabel>VPS Profile</InputLabel>
          <Select
            value={config.vpsOptimization.vpsProfile}
            onChange={(e) => handleConfigChange('vpsOptimization', 'vpsProfile', e.target.value)}
          >
            <MenuItem value="auto">Auto-detect</MenuItem>
            <MenuItem value="micro">Micro (1 CPU, 1GB RAM)</MenuItem>
            <MenuItem value="small">Small (2 CPU, 2GB RAM)</MenuItem>
            <MenuItem value="medium">Medium (4 CPU, 4GB RAM)</MenuItem>
            <MenuItem value="large">Large (8 CPU, 8GB RAM)</MenuItem>
            <MenuItem value="xlarge">XLarge (16+ CPU, 16+ GB RAM)</MenuItem>
          </Select>
        </FormControl>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormGroup>
          <FormControlLabel
            control={
              <Switch
                checked={config.vpsOptimization.enableAutoOptimization}
                onChange={(e) => handleConfigChange('vpsOptimization', 'enableAutoOptimization', e.target.checked)}
              />
            }
            label="Enable Auto-Optimization"
          />
        </FormGroup>
      </Grid>
      
      <Grid item xs={12}>
        <Typography variant="subtitle2" gutterBottom>
          Resource Optimization
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={6} sm={3}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={config.vpsOptimization.enableResourceManagement}
                  onChange={(e) => handleConfigChange('vpsOptimization', 'enableResourceManagement', e.target.checked)}
                />
              }
              label="Resource Management"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={config.vpsOptimization.enableMemoryOptimization}
                  onChange={(e) => handleConfigChange('vpsOptimization', 'enableMemoryOptimization', e.target.checked)}
                />
              }
              label="Memory Optimization"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={config.vpsOptimization.enableCPUOptimization}
                  onChange={(e) => handleConfigChange('vpsOptimization', 'enableCPUOptimization', e.target.checked)}
                />
              }
              label="CPU Optimization"
            />
          </Grid>
          <Grid item xs={6} sm={3}>
            <FormControlLabel
              control={
                <Checkbox
                  checked={config.vpsOptimization.enableNetworkOptimization}
                  onChange={(e) => handleConfigChange('vpsOptimization', 'enableNetworkOptimization', e.target.checked)}
                />
              }
              label="Network Optimization"
            />
          </Grid>
        </Grid>
      </Grid>
    </Grid>
  );

  const renderAdvancedConfiguration = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} sm={6}>
        <FormGroup>
          <FormControlLabel
            control={
              <Switch
                checked={config.advanced.includeHiddenElements}
                onChange={(e) => handleConfigChange('advanced', 'includeHiddenElements', e.target.checked)}
              />
            }
            label="Include Hidden Elements"
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.advanced.enableManualReview}
                onChange={(e) => handleConfigChange('advanced', 'enableManualReview', e.target.checked)}
              />
            }
            label="Enable Manual Review"
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.advanced.enableDetailedReporting}
                onChange={(e) => handleConfigChange('advanced', 'enableDetailedReporting', e.target.checked)}
              />
            }
            label="Detailed Reporting"
          />
          <FormControlLabel
            control={
              <Switch
                checked={config.advanced.enableScreenshots}
                onChange={(e) => handleConfigChange('advanced', 'enableScreenshots', e.target.checked)}
              />
            }
            label="Capture Screenshots"
          />
        </FormGroup>
      </Grid>
      
      <Grid item xs={12} sm={6}>
        <FormControl fullWidth>
          <FormControlLabel
            control={
              <Switch
                checked={config.authentication.requiresAuth}
                onChange={(e) => handleConfigChange('authentication', 'requiresAuth', e.target.checked)}
              />
            }
            label="Requires Authentication"
          />
        </FormControl>
        
        {config.authentication.requiresAuth && (
          <Box mt={2}>
            <FormControl fullWidth mb={2}>
              <InputLabel>Authentication Type</InputLabel>
              <Select
                value={config.authentication.authType}
                onChange={(e) => handleConfigChange('authentication', 'authType', e.target.value)}
              >
                <MenuItem value="basic">Basic Auth</MenuItem>
                <MenuItem value="form">Form Login</MenuItem>
                <MenuItem value="oauth">OAuth</MenuItem>
              </Select>
            </FormControl>
          </Box>
        )}
      </Grid>
    </Grid>
  );

  return (
    <Box>
      <Typography variant="h5" gutterBottom>
        Enhanced WCAG Scan Configuration
      </Typography>
      
      <Typography variant="body2" color="textSecondary" paragraph>
        Configure your accessibility scan with advanced Phase 1-3 enhancements including performance optimization, 
        intelligent website analysis, and VPS resource management.
      </Typography>

      {/* Basic Configuration */}
      <Accordion 
        expanded={expandedSection === 'basic'} 
        onChange={handleAccordionChange('basic')}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <SettingsIcon sx={{ mr: 1 }} />
          <Typography variant="h6">Basic Configuration</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {renderBasicConfiguration()}
        </AccordionDetails>
      </Accordion>

      {/* Performance Configuration */}
      <Accordion 
        expanded={expandedSection === 'performance'} 
        onChange={handleAccordionChange('performance')}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <SpeedIcon sx={{ mr: 1 }} />
          <Typography variant="h6">Performance Optimization</Typography>
          <Chip label="Phase 1" size="small" color="primary" sx={{ ml: 1 }} />
        </AccordionSummary>
        <AccordionDetails>
          {renderPerformanceConfiguration()}
        </AccordionDetails>
      </Accordion>

      {/* Website Analysis Configuration */}
      <Accordion 
        expanded={expandedSection === 'analysis'} 
        onChange={handleAccordionChange('analysis')}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <AIIcon sx={{ mr: 1 }} />
          <Typography variant="h6">Website Analysis</Typography>
          <Chip label="Phase 2" size="small" color="secondary" sx={{ ml: 1 }} />
        </AccordionSummary>
        <AccordionDetails>
          {renderWebsiteAnalysisConfiguration()}
        </AccordionDetails>
      </Accordion>

      {/* VPS Optimization Configuration */}
      <Accordion 
        expanded={expandedSection === 'vps'} 
        onChange={handleAccordionChange('vps')}
        sx={{ mb: 2 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <MemoryIcon sx={{ mr: 1 }} />
          <Typography variant="h6">VPS Optimization</Typography>
          <Chip label="Phase 3" size="small" color="success" sx={{ ml: 1 }} />
        </AccordionSummary>
        <AccordionDetails>
          {renderVPSOptimizationConfiguration()}
        </AccordionDetails>
      </Accordion>

      {/* Advanced Configuration */}
      <Accordion 
        expanded={expandedSection === 'advanced'} 
        onChange={handleAccordionChange('advanced')}
        sx={{ mb: 3 }}
      >
        <AccordionSummary expandIcon={<ExpandMoreIcon />}>
          <SecurityIcon sx={{ mr: 1 }} />
          <Typography variant="h6">Advanced Options</Typography>
        </AccordionSummary>
        <AccordionDetails>
          {renderAdvancedConfiguration()}
        </AccordionDetails>
      </Accordion>

      {/* Action Buttons */}
      <Box display="flex" justifyContent="flex-end" gap={2}>
        <Button
          variant="contained"
          size="large"
          onClick={() => onStartScan(config)}
          disabled={!config.targetUrl || isScanning}
          sx={{ minWidth: 200 }}
        >
          {isScanning ? 'Scanning...' : 'Start Enhanced Scan'}
        </Button>
      </Box>
    </Box>
  );
};

export default EnhancedScanConfiguration;
