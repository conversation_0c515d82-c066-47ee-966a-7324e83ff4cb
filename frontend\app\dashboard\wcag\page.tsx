'use client';

/**
 * WCAG Dashboard Main Page
 * Overview dashboard for WCAG compliance with recent scans and quick actions
 */

import React, { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  PlayCircle,
  History,
  FileText,
  Settings,
  TrendingUp,
  AlertTriangle,
  CheckCircle,
  Clock,
  Globe,
} from 'lucide-react';
import { useWcagState, useWcagActions } from '@/context/WcagContext';
import { WcagProvider } from '@/context/WcagContext';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';

/**
 * WCAG Dashboard Content Component
 */
const WcagDashboardContent: React.FC = () => {
  const router = useRouter();
  const state = useWcagState();
  const actions = useWcagActions();
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const [mounted, setMounted] = useState(false);

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  const loadDashboardData = useCallback(async () => {
    try {
      // Try to fetch dashboard data first, fallback to scans if needed
      try {
        await actions.fetchDashboardData?.();
      } catch (dashboardError) {
        // eslint-disable-next-line no-console
        console.warn('Dashboard endpoint failed, falling back to scans:', dashboardError);
        await actions.fetchScans({ limit: 5 });
      }
    } catch (error) {
      // eslint-disable-next-line no-console
      console.error('Error loading dashboard data:', error);
    }
  }, [actions.fetchScans, actions.fetchDashboardData]);

  // Load initial data
  useEffect(() => {
    if (mounted) {
      loadDashboardData();
    }
  }, [mounted, loadDashboardData]);

  const handleStartScan = () => {
    router.push('/dashboard/wcag/scan');
  };

  const handleViewHistory = () => {
    router.push('/dashboard/wcag/history');
  };

  const handleViewReports = () => {
    router.push('/dashboard/wcag/reports');
  };

  const handleViewSettings = () => {
    router.push('/dashboard/wcag/settings');
  };

  const handleViewScanDetails = (scanId: string) => {
    router.push(`/dashboard/wcag/scan/${scanId}`);
  };

  // Calculate dashboard metrics
  const totalScans = state.scans.length;
  const completedScans = state.scans.filter((scan) => scan.status === 'completed').length;
  const runningScans = Object.values(state.scanProgressMap).filter(
    (progress) => progress.status === 'running' || progress.status === 'pending',
  ).length;
  const averageScore =
    completedScans > 0
      ? state.scans
          .filter((scan) => scan.status === 'completed')
          .reduce((sum, scan) => sum + (Number(scan.overallScore) || 0), 0) / completedScans
      : 0;

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <WcagBreadcrumb />

      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
            WCAG Compliance Dashboard
          </h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Monitor and manage web accessibility compliance
          </p>
        </div>
        <Button onClick={handleStartScan} className="flex items-center gap-2">
          <PlayCircle className="h-4 w-4" />
          Start New Scan
        </Button>
      </div>

      {/* Error Alert */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Total Scans</p>
                <p className="text-2xl font-bold text-gray-900 dark:text-white">{totalScans}</p>
              </div>
              <Globe className="h-8 w-8 text-blue-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Completed</p>
                <p className="text-2xl font-bold text-green-600">{completedScans}</p>
              </div>
              <CheckCircle className="h-8 w-8 text-green-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Running</p>
                <p className="text-2xl font-bold text-orange-600">{runningScans}</p>
              </div>
              <Clock className="h-8 w-8 text-orange-500" />
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm font-medium text-gray-600 dark:text-gray-400">Avg. Score</p>
                <p className="text-2xl font-bold text-purple-600">{Number(averageScore).toFixed(1)}%</p>
              </div>
              <TrendingUp className="h-8 w-8 text-purple-500" />
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Quick Actions */}
      <Card>
        <CardHeader>
          <CardTitle>Quick Actions</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
            <Button
              variant="outline"
              className="h-20 flex flex-col items-center gap-2"
              onClick={handleStartScan}
            >
              <PlayCircle className="h-6 w-6" />
              <span>Start Scan</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex flex-col items-center gap-2"
              onClick={handleViewHistory}
            >
              <History className="h-6 w-6" />
              <span>View History</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex flex-col items-center gap-2"
              onClick={handleViewReports}
            >
              <FileText className="h-6 w-6" />
              <span>Reports</span>
            </Button>

            <Button
              variant="outline"
              className="h-20 flex flex-col items-center gap-2"
              onClick={handleViewSettings}
            >
              <Settings className="h-6 w-6" />
              <span>Settings</span>
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Recent Scans */}
      <Card>
        <CardHeader className="flex flex-row items-center justify-between">
          <CardTitle>Recent Scans</CardTitle>
          <Button variant="outline" size="sm" onClick={handleViewHistory}>
            View All
          </Button>
        </CardHeader>
        <CardContent>
          {state.loading.fetching ? (
            <div className="flex items-center justify-center py-8">
              <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
            </div>
          ) : state.recentScans.length === 0 ? (
            <div className="text-center py-8">
              <Globe className="h-12 w-12 text-gray-400 mx-auto mb-4" />
              <p className="text-gray-500 dark:text-gray-400">
                No scans yet. Start your first WCAG compliance scan!
              </p>
              <Button className="mt-4" onClick={handleStartScan}>
                Start First Scan
              </Button>
            </div>
          ) : (
            <div className="space-y-4">
              {state.recentScans.slice(0, 5).map((scan) => (
                <div
                  key={scan.scanId}
                  className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 dark:hover:bg-gray-800 cursor-pointer transition-colors"
                  onClick={() => handleViewScanDetails(scan.scanId)}
                >
                  <div className="flex items-center gap-4">
                    <div className="flex-shrink-0">
                      {scan.status === 'completed' ? (
                        <CheckCircle className="h-5 w-5 text-green-500" />
                      ) : scan.status === 'running' ? (
                        <Clock className="h-5 w-5 text-orange-500" />
                      ) : (
                        <AlertTriangle className="h-5 w-5 text-red-500" />
                      )}
                    </div>
                    <div>
                      <p className="font-medium text-gray-900 dark:text-white">{scan.targetUrl}</p>
                      <p className="text-sm text-gray-500 dark:text-gray-400">
                        {new Date(scan.metadata.startTime).toLocaleDateString()} • WCAG{' '}
                        {scan.metadata.version} • Level {scan.levelAchieved}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-4">
                    {scan.status === 'completed' && (
                      <div className="text-right">
                        <p className="font-medium text-gray-900 dark:text-white">
                          {scan.overallScore ? Number(scan.overallScore).toFixed(1) : '0.0'}%
                        </p>
                        <Badge
                          variant={
                            (Number(scan.overallScore) || 0) >= 90
                              ? 'default'
                              : (Number(scan.overallScore) || 0) >= 70
                                ? 'secondary'
                                : 'destructive'
                          }
                        >
                          {scan.levelAchieved || 'N/A'}
                        </Badge>
                      </div>
                    )}
                    {scan.status === 'running' && state.scanProgressMap[scan.scanId] && (
                      <div className="w-24">
                        <Progress
                          value={state.scanProgressMap[scan.scanId].progress}
                          className="h-2"
                        />
                        <p className="text-xs text-gray-500 mt-1">
                          {state.scanProgressMap[scan.scanId].progress}%
                        </p>
                      </div>
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Running Scans */}
      {runningScans > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Clock className="h-5 w-5 text-orange-500" />
              Running Scans ({runningScans})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {Object.entries(state.scanProgressMap)
                .filter(
                  ([, progress]) => progress.status === 'running' || progress.status === 'pending',
                )
                .map(([scanId, progress]) => (
                  <div key={scanId} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <p className="font-medium text-gray-900 dark:text-white">
                        Scan {scanId.slice(0, 8)}...
                      </p>
                      <Badge variant="secondary">{progress.status}</Badge>
                    </div>
                    <Progress value={progress.progress} className="h-2 mb-2" />
                    <div className="flex justify-between text-sm text-gray-500 dark:text-gray-400">
                      <span>{progress.currentCheck || 'Initializing...'}</span>
                      <span>{progress.progress}%</span>
                    </div>
                  </div>
                ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
};

/**
 * Main WCAG Dashboard Page with Provider
 */
export default function WcagDashboardPage() {
  return (
    <WcagProvider>
      <WcagDashboardContent />
    </WcagProvider>
  );
}
