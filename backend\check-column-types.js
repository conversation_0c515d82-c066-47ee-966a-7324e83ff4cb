/**
 * Check Column Types
 * Verify the actual column types in the database
 */

console.log('🔍 Checking database column types...');

// Load environment variables
const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value && !key.startsWith('#')) {
      process.env[key.trim()] = value.trim();
    }
  });
}

process.env.NODE_ENV = 'development';

async function checkColumnTypes() {
  try {
    // Set up TypeScript compilation
    require('ts-node/register');
    require('tsconfig-paths/register');
    
    console.log('✅ TypeScript setup complete');
    
    // Import database connection
    const db = require('./src/lib/db').default;
    console.log('✅ Database connection imported');
    
    // Check if the table exists
    const tableExists = await db.schema.hasTable('wcag_automated_results');
    console.log('📋 Table exists:', tableExists);
    
    if (!tableExists) {
      console.log('❌ Table does not exist. Run migrations first.');
      return;
    }
    
    // Get column information
    const columns = await db.raw(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'wcag_automated_results' 
      AND column_name IN ('evidence', 'recommendations')
      ORDER BY column_name
    `);
    
    console.log('📊 Column information:');
    columns.rows.forEach(col => {
      console.log(`  ${col.column_name}: ${col.data_type} (${col.udt_name})`);
    });
    
    // Check if columns are JSON or JSONB
    const evidenceCol = columns.rows.find(col => col.column_name === 'evidence');
    const recommendationsCol = columns.rows.find(col => col.column_name === 'recommendations');
    
    if (evidenceCol) {
      if (evidenceCol.udt_name === 'jsonb') {
        console.log('✅ Evidence column is JSONB');
      } else if (evidenceCol.udt_name === 'json') {
        console.log('❌ Evidence column is JSON (should be JSONB)');
      } else {
        console.log('⚠️ Evidence column type:', evidenceCol.udt_name);
      }
    }
    
    if (recommendationsCol) {
      if (recommendationsCol.udt_name === 'jsonb') {
        console.log('✅ Recommendations column is JSONB');
      } else if (recommendationsCol.udt_name === 'json') {
        console.log('❌ Recommendations column is JSON (should be JSONB)');
      } else {
        console.log('⚠️ Recommendations column type:', recommendationsCol.udt_name);
      }
    }
    
    // If columns are JSON instead of JSONB, we need to fix them
    if (evidenceCol?.udt_name === 'json' || recommendationsCol?.udt_name === 'json') {
      console.log('\n🔧 FIXING COLUMN TYPES...');
      
      try {
        if (evidenceCol?.udt_name === 'json') {
          console.log('🔧 Converting evidence column from JSON to JSONB...');
          await db.raw('ALTER TABLE wcag_automated_results ALTER COLUMN evidence TYPE jsonb USING evidence::jsonb');
          console.log('✅ Evidence column converted to JSONB');
        }
        
        if (recommendationsCol?.udt_name === 'json') {
          console.log('🔧 Converting recommendations column from JSON to JSONB...');
          await db.raw('ALTER TABLE wcag_automated_results ALTER COLUMN recommendations TYPE jsonb USING recommendations::jsonb');
          console.log('✅ Recommendations column converted to JSONB');
        }
        
        console.log('🎉 Column types fixed successfully!');
        
        // Verify the fix
        const updatedColumns = await db.raw(`
          SELECT column_name, data_type, udt_name 
          FROM information_schema.columns 
          WHERE table_name = 'wcag_automated_results' 
          AND column_name IN ('evidence', 'recommendations')
          ORDER BY column_name
        `);
        
        console.log('📊 Updated column information:');
        updatedColumns.rows.forEach(col => {
          console.log(`  ${col.column_name}: ${col.data_type} (${col.udt_name})`);
        });
        
      } catch (alterError) {
        console.error('❌ Failed to fix column types:', alterError);
      }
    }
    
    console.log('🎉 Column type check completed!');
    
  } catch (error) {
    console.error('❌ Column type check failed:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code
    });
    
    if (error.message.includes('connect') || error.message.includes('database')) {
      console.log('💡 Database connection issue. Make sure PostgreSQL is running.');
    }
    
    process.exit(1);
  }
}

// Run the check
console.log('🔧 Starting column type check...');
checkColumnTypes()
  .then(() => {
    console.log('🎉 Column type check completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Column type check failed:', error);
    process.exit(1);
  });
