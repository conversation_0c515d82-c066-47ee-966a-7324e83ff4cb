'use client';

/**
 * WCAG Context Provider
 * State management for WCAG compliance functionality using React Context
 */

import React, { createContext, useContext, useReducer, useCallback, useMemo } from 'react';
import {
  WcagScanResult,
  WcagScanFormData,
  ScanProgressInfo,
  QueueStatusInfo,
  WcagDashboardState,
  ScanStatus,
  WcagLevel,
  WcagRiskLevel,
} from '../types/wcag';
import { wcagApiService } from '../services/wcag-api';

// Extended state interface for context
interface WcagContextState extends WcagDashboardState {
  scans: WcagScanResult[];
  scanProgressMap: Record<string, ScanProgressInfo>;
  queueStatus: QueueStatusInfo | null;
  loading: {
    scanning: boolean;
    fetching: boolean;
    exporting: boolean;
    deleting: boolean;
  };
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

// Action types
type WcagAction =
  | { type: 'SET_LOADING'; payload: { key: keyof WcagContextState['loading']; value: boolean } }
  | { type: 'SET_ERROR'; payload: string | undefined }
  | { type: 'SET_SELECTED_SCAN'; payload: string | undefined }
  | { type: 'SET_CURRENT_SCAN'; payload: WcagScanResult | undefined }
  | {
      type: 'SET_SCANS';
      payload: { scans: WcagScanResult[]; pagination: WcagContextState['pagination'] };
    }
  | { type: 'UPDATE_SCAN_PROGRESS'; payload: ScanProgressInfo }
  | { type: 'UPDATE_QUEUE_STATUS'; payload: QueueStatusInfo }
  | { type: 'SET_PAGINATION'; payload: { page?: number; limit?: number } }
  | { type: 'ADD_SCAN'; payload: WcagScanResult }
  | { type: 'REMOVE_SCAN'; payload: string }
  | { type: 'RESET_STATE' };

// Context interface
interface WcagContextType {
  state: WcagContextState;
  actions: {
    startScan: (formData: WcagScanFormData) => Promise<WcagScanResult>;
    fetchScans: (params?: { page?: number; limit?: number; status?: string }) => Promise<void>;
    fetchDashboardData: () => Promise<void>;
    fetchScanDetails: (scanId: string) => Promise<WcagScanResult>;
    deleteScan: (scanId: string) => Promise<void>;
    exportScan: (
      scanId: string,
      format: 'pdf' | 'json' | 'csv',
      options?: {
        includeEvidence?: boolean;
        includeRecommendations?: boolean;
        includeManualReviewItems?: boolean;
      },
    ) => Promise<void>;
    setSelectedScan: (scanId: string | undefined) => void;
    updateScanProgress: (progress: ScanProgressInfo) => void;
    updateQueueStatus: (status: QueueStatusInfo) => void;
    setPagination: (pagination: { page?: number; limit?: number }) => void;
    clearError: () => void;
    resetState: () => void;
  };
}

// Initial state
const initialState: WcagContextState = {
  currentScan: undefined,
  recentScans: [],
  isScanning: false,
  scanProgress: 0,
  error: undefined,
  selectedScanId: undefined,
  scans: [],
  scanProgressMap: {},
  queueStatus: null,
  loading: {
    scanning: false,
    fetching: false,
    exporting: false,
    deleting: false,
  },
  pagination: {
    page: 1,
    limit: 20,
    total: 0,
    totalPages: 0,
  },
};

// Reducer function
const wcagReducer = (state: WcagContextState, action: WcagAction): WcagContextState => {
  switch (action.type) {
    case 'SET_LOADING':
      return {
        ...state,
        loading: {
          ...state.loading,
          [action.payload.key]: action.payload.value,
        },
      };

    case 'SET_ERROR':
      return {
        ...state,
        error: action.payload,
      };

    case 'SET_SELECTED_SCAN':
      return {
        ...state,
        selectedScanId: action.payload,
        currentScan: action.payload
          ? state.scans.find((scan) => scan.scanId === action.payload)
          : undefined,
      };

    case 'SET_CURRENT_SCAN':
      return {
        ...state,
        currentScan: action.payload,
      };

    case 'SET_SCANS':
      return {
        ...state,
        scans: action.payload.scans,
        pagination: action.payload.pagination,
        recentScans: action.payload.scans
          .filter((scan) => scan.status === 'completed')
          .slice(0, 10),
      };

    case 'UPDATE_SCAN_PROGRESS': {
      const newScanProgressMap = {
        ...state.scanProgressMap,
        [action.payload.scanId]: action.payload,
      };

      const hasRunningScans = Object.values(newScanProgressMap).some(
        (progress: ScanProgressInfo) =>
          progress.status === 'running' || progress.status === 'pending',
      );

      return {
        ...state,
        scanProgressMap: newScanProgressMap,
        isScanning: hasRunningScans,
      };
    }

    case 'UPDATE_QUEUE_STATUS':
      return {
        ...state,
        queueStatus: action.payload,
      };

    case 'SET_PAGINATION':
      return {
        ...state,
        pagination: {
          ...state.pagination,
          ...action.payload,
        },
      };

    case 'ADD_SCAN': {
      const updatedScans = [action.payload, ...state.scans];
      return {
        ...state,
        scans: updatedScans,
        currentScan: action.payload,
        recentScans: [action.payload, ...state.recentScans.slice(0, 9)],
      };
    }

    case 'REMOVE_SCAN':
      return {
        ...state,
        scans: state.scans.filter((scan) => scan.scanId !== action.payload),
        recentScans: state.recentScans.filter((scan) => scan.scanId !== action.payload),
        currentScan: state.currentScan?.scanId === action.payload ? undefined : state.currentScan,
        selectedScanId: state.selectedScanId === action.payload ? undefined : state.selectedScanId,
      };

    case 'RESET_STATE':
      return initialState;

    default:
      return state;
  }
};

// Create context
const WcagContext = createContext<WcagContextType | undefined>(undefined);

// Provider component
export const WcagProvider: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const [state, dispatch] = useReducer(wcagReducer, initialState);

  // Action creators
  const startScan = useCallback(async (formData: WcagScanFormData): Promise<WcagScanResult> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: { key: 'scanning', value: true } });
      dispatch({ type: 'SET_ERROR', payload: undefined });

      const result = await wcagApiService.startScan(formData);
      dispatch({ type: 'ADD_SCAN', payload: result });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to start scan';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { key: 'scanning', value: false } });
    }
  }, []);

  const fetchScans = useCallback(
    async (params: { page?: number; limit?: number; status?: string } = {}) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: { key: 'fetching', value: true } });
        dispatch({ type: 'SET_ERROR', payload: undefined });

        const result = await wcagApiService.getScans(params);

        // Transform scans to match expected format
        const transformedScans = result.scans.map(
          (scan): WcagScanResult => ({
            scanId: scan.scanId,
            targetUrl: scan.targetUrl,
            status: scan.status as ScanStatus,
            overallScore: scan.overallScore || 0,
            levelAchieved: (scan.levelAchieved as WcagLevel) || 'A',
            riskLevel: (scan.riskLevel as WcagRiskLevel) || 'low',
            checks: [],
            recommendations: [],
            summary: {
              totalAutomatedChecks: scan.totalAutomatedChecks || 0,
              passedAutomatedChecks: scan.passedAutomatedChecks || 0,
              failedAutomatedChecks: scan.failedAutomatedChecks || 0,
              automatedScore: scan.overallScore || 0,
              automationRate: 0.87,
              manualReviewItems: scan.manualReviewItems || 0,
              categoryScores: { perceivable: 0, operable: 0, understandable: 0, robust: 0 },
              versionScores: { wcag21: 0, wcag22: 0, wcag30: 0 },
            },
            metadata: {
              scanId: scan.scanId,
              userId: '',
              requestId: scan.scanId,
              startTime: new Date(scan.scanTimestamp),
              duration: 0,
              userAgent: '',
              viewport: { width: 1920, height: 1080 },
              environment: 'production',
              version: '1.0.0',
            },
            // Add missing required properties
            url: scan.targetUrl,
            scanTimestamp: scan.scanTimestamp,
            completionTimestamp: scan.completionTimestamp,
            wcagVersion: 'all',
            complianceLevel: (scan.levelAchieved as WcagLevel) || 'A',
            scanOptions: {
              maxPages: 5,
              enableContrastAnalysis: true,
              enableKeyboardTesting: true,
              enableFocusAnalysis: true,
              enableSemanticValidation: true,
              wcagVersion: 'all',
              level: (scan.levelAchieved as WcagLevel) || 'A',
            },
          }),
        );

        dispatch({
          type: 'SET_SCANS',
          payload: {
            scans: transformedScans,
            pagination: result.pagination,
          },
        });
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to fetch scans';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'fetching', value: false } });
      }
    },
    [],
  );

  const fetchDashboardData = useCallback(async (): Promise<void> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: { key: 'fetching', value: true } });
      dispatch({ type: 'SET_ERROR', payload: undefined });

      const dashboardData = await wcagApiService.getDashboardData();

      // Transform recent scans to match WcagScanResult interface
      const transformedScans = dashboardData.recentScans.map(
        (scan): WcagScanResult => ({
          scanId: scan.scanId,
          targetUrl: scan.targetUrl,
          status: scan.status as ScanStatus,
          overallScore: scan.overallScore || 0,
          levelAchieved: (scan.levelAchieved as WcagLevel) || 'A',
          riskLevel: (scan.riskLevel as WcagRiskLevel) || 'low',
          checks: [],
          recommendations: [],
          summary: {
            totalAutomatedChecks: scan.totalAutomatedChecks || 0,
            passedAutomatedChecks: scan.passedAutomatedChecks || 0,
            failedAutomatedChecks: scan.failedAutomatedChecks || 0,
            automatedScore: scan.overallScore || 0,
            automationRate: 0.87,
            manualReviewItems: scan.manualReviewItems || 0,
            categoryScores: { perceivable: 0, operable: 0, understandable: 0, robust: 0 },
            versionScores: { wcag21: 0, wcag22: 0, wcag30: 0 },
          },
          metadata: {
            scanId: scan.scanId,
            userId: '',
            requestId: scan.scanId,
            startTime: new Date(scan.scanTimestamp),
            duration: 0,
            userAgent: '',
            viewport: { width: 1920, height: 1080 },
            environment: 'production',
            version: '1.0.0',
          },
          url: scan.targetUrl,
          scanTimestamp: scan.scanTimestamp,
          completionTimestamp: scan.completionTimestamp,
          wcagVersion: 'all',
          complianceLevel: (scan.levelAchieved as WcagLevel) || 'A',
          scanOptions: {
            maxPages: 5,
            enableContrastAnalysis: true,
            enableKeyboardTesting: true,
            enableFocusAnalysis: true,
            enableSemanticValidation: true,
            wcagVersion: 'all',
            level: (scan.levelAchieved as WcagLevel) || 'A',
          },
        }),
      );

      // Update state with dashboard data
      dispatch({
        type: 'SET_SCANS',
        payload: {
          scans: transformedScans,
          pagination: {
            page: 1,
            limit: transformedScans.length,
            total: dashboardData.overview.totalScans,
            totalPages: 1,
          },
        },
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch dashboard data';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { key: 'fetching', value: false } });
    }
  }, []);

  const fetchScanDetails = useCallback(async (scanId: string): Promise<WcagScanResult> => {
    try {
      dispatch({ type: 'SET_LOADING', payload: { key: 'fetching', value: true } });
      dispatch({ type: 'SET_ERROR', payload: undefined });

      const result = await wcagApiService.getScanDetails(scanId);
      dispatch({ type: 'SET_CURRENT_SCAN', payload: result });

      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to fetch scan details';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { key: 'fetching', value: false } });
    }
  }, []);

  const deleteScan = useCallback(async (scanId: string) => {
    try {
      dispatch({ type: 'SET_LOADING', payload: { key: 'deleting', value: true } });
      dispatch({ type: 'SET_ERROR', payload: undefined });

      await wcagApiService.deleteScan(scanId);
      dispatch({ type: 'REMOVE_SCAN', payload: scanId });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Failed to delete scan';
      dispatch({ type: 'SET_ERROR', payload: errorMessage });
      throw error;
    } finally {
      dispatch({ type: 'SET_LOADING', payload: { key: 'deleting', value: false } });
    }
  }, []);

  const exportScan = useCallback(
    async (
      scanId: string,
      format: 'pdf' | 'json' | 'csv',
      options?: {
        includeEvidence?: boolean;
        includeRecommendations?: boolean;
        includeManualReviewItems?: boolean;
      },
    ) => {
      try {
        dispatch({ type: 'SET_LOADING', payload: { key: 'exporting', value: true } });
        dispatch({ type: 'SET_ERROR', payload: undefined });

        const blob = await wcagApiService.exportScan(scanId, format, options);

        // Create download link
        const url = window.URL.createObjectURL(blob);
        const link = document.createElement('a');
        link.href = url;
        link.download = `wcag-report-${scanId}.${format}`;
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        window.URL.revokeObjectURL(url);
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : 'Failed to export scan';
        dispatch({ type: 'SET_ERROR', payload: errorMessage });
        throw error;
      } finally {
        dispatch({ type: 'SET_LOADING', payload: { key: 'exporting', value: false } });
      }
    },
    [],
  );

  // Simple action creators
  const setSelectedScan = useCallback((scanId: string | undefined) => {
    dispatch({ type: 'SET_SELECTED_SCAN', payload: scanId });
  }, []);

  const updateScanProgress = useCallback((progress: ScanProgressInfo) => {
    dispatch({ type: 'UPDATE_SCAN_PROGRESS', payload: progress });
  }, []);

  const updateQueueStatus = useCallback((status: QueueStatusInfo) => {
    dispatch({ type: 'UPDATE_QUEUE_STATUS', payload: status });
  }, []);

  const setPagination = useCallback((pagination: { page?: number; limit?: number }) => {
    dispatch({ type: 'SET_PAGINATION', payload: pagination });
  }, []);

  const clearError = useCallback(() => {
    dispatch({ type: 'SET_ERROR', payload: undefined });
  }, []);

  const resetState = useCallback(() => {
    dispatch({ type: 'RESET_STATE' });
  }, []);

  // Memoize actions to prevent infinite re-renders
  const actions = useMemo(
    () => ({
      startScan,
      fetchScans,
      fetchDashboardData,
      fetchScanDetails,
      deleteScan,
      exportScan,
      setSelectedScan,
      updateScanProgress,
      updateQueueStatus,
      setPagination,
      clearError,
      resetState,
    }),
    [
      startScan,
      fetchScans,
      fetchDashboardData,
      fetchScanDetails,
      deleteScan,
      exportScan,
      setSelectedScan,
      updateScanProgress,
      updateQueueStatus,
      setPagination,
      clearError,
      resetState,
    ],
  );

  // Context value
  const contextValue: WcagContextType = useMemo(
    () => ({
      state,
      actions,
    }),
    [state, actions],
  );

  return <WcagContext.Provider value={contextValue}>{children}</WcagContext.Provider>;
};

// Custom hook to use WCAG context
export const useWcag = (): WcagContextType => {
  const context = useContext(WcagContext);
  if (context === undefined) {
    throw new Error('useWcag must be used within a WcagProvider');
  }
  return context;
};

// Selector hooks for convenience
export const useWcagState = () => {
  const { state } = useWcag();
  return state;
};

export const useWcagActions = () => {
  const { actions } = useWcag();
  return actions;
};

export const useWcagScanning = () => {
  const { state } = useWcag();
  return {
    isScanning: state.isScanning,
    scanProgress: state.scanProgress,
    queueStatus: state.queueStatus,
  };
};

export const useWcagCurrentScan = () => {
  const { state } = useWcag();
  return {
    currentScan: state.currentScan,
    selectedScanId: state.selectedScanId,
  };
};
