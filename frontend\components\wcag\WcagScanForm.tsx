/**
 * WCAG Scan Form Component
 * Form for initiating new WCAG compliance scans
 */

'use client';

import React, { useState, useCallback } from 'react';
import { Play, Settings, RotateCcw } from 'lucide-react';
import { WcagScanFormData, WcagVersion, WcagLevel } from '../../types/wcag';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { Button } from '../ui/button';
import { Input } from '../ui/input';
import { Label } from '../ui/label';
import { Checkbox } from '../ui/checkbox';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Alert } from '../ui/alert';

import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '../ui/collapsible';

interface WcagScanFormProps {
  onSubmit: (formData: WcagScanFormData) => Promise<void>;
  isLoading?: boolean;
  error?: string;
  disabled?: boolean;
}

const WcagScanForm: React.FC<WcagScanFormProps> = ({
  onSubmit,
  isLoading = false,
  error,
  disabled = false,
}) => {
  const [formData, setFormData] = useState<WcagScanFormData>({
    targetUrl: '',
    enableContrastAnalysis: true,
    enableKeyboardTesting: true,
    enableFocusAnalysis: true,
    enableSemanticValidation: true,
    enableManualReview: false,
    wcagVersion: 'all',
    level: 'AA',
    maxPages: 5,
  });

  const [showAdvanced, setShowAdvanced] = useState(false);
  const [urlError, setUrlError] = useState<string>('');

  /**
   * Validate URL format
   */
  const validateUrl = useCallback((url: string): boolean => {
    try {
      new URL(url);
      return true;
    } catch {
      return false;
    }
  }, []);

  /**
   * Handle form field changes
   */
  const handleChange = useCallback(
    (
      field: keyof WcagScanFormData,
      value: string | boolean | number | WcagVersion | WcagLevel | 'all',
    ) => {
      setFormData((prev) => ({
        ...prev,
        [field]: value,
      }));

      // Validate URL on change
      if (field === 'targetUrl') {
        if (value && typeof value === 'string' && !validateUrl(value)) {
          setUrlError('Please enter a valid URL (including http:// or https://)');
        } else {
          setUrlError('');
        }
      }
    },
    [validateUrl],
  );

  /**
   * Handle form submission
   */
  const handleSubmit = useCallback(
    async (event: React.FormEvent) => {
      event.preventDefault();

      // Final validation
      if (!formData.targetUrl) {
        setUrlError('URL is required');
        return;
      }

      if (!validateUrl(formData.targetUrl)) {
        setUrlError('Please enter a valid URL');
        return;
      }

      try {
        await onSubmit(formData);
      } catch (error) {
        // eslint-disable-next-line no-console
        // eslint-disable-next-line no-console
        console.error('Form submission failed:', error);
      }
    },
    [formData, onSubmit, validateUrl],
  );

  /**
   * Reset form to defaults
   */
  const handleReset = useCallback(() => {
    setFormData({
      targetUrl: '',
      enableContrastAnalysis: true,
      enableKeyboardTesting: true,
      enableFocusAnalysis: true,
      enableSemanticValidation: true,
      enableManualReview: false,
      wcagVersion: 'all',
      level: 'AA',
      maxPages: 5,
    });
    setUrlError('');
  }, []);

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Play className="h-5 w-5" />
          Start WCAG Compliance Scan
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Analyze your website for WCAG 2.1, 2.2, and 3.0 compliance
        </p>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          {error && (
            <Alert variant="destructive">
              <p>{error}</p>
            </Alert>
          )}

          {/* Target URL */}
          <div className="space-y-2">
            <Label htmlFor="targetUrl">Website URL *</Label>
            <Input
              id="targetUrl"
              type="url"
              placeholder="https://example.com"
              value={formData.targetUrl}
              onChange={(e) => handleChange('targetUrl', e.target.value)}
              disabled={disabled || isLoading}
              className={urlError ? 'border-destructive' : ''}
            />
            {urlError && <p className="text-sm text-destructive">{urlError}</p>}
            {!urlError && (
              <p className="text-sm text-muted-foreground">Enter the URL of the website to scan</p>
            )}
          </div>

          {/* WCAG Version and Level Selection */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="wcagVersion">WCAG Version</Label>
              <Select
                value={formData.wcagVersion}
                onValueChange={(value) => handleChange('wcagVersion', value as WcagVersion | 'all')}
                disabled={disabled || isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select WCAG version" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Versions (2.1, 2.2, 3.0)</SelectItem>
                  <SelectItem value="2.1">WCAG 2.1</SelectItem>
                  <SelectItem value="2.2">WCAG 2.2</SelectItem>
                  <SelectItem value="3.0">WCAG 3.0 (Draft)</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="level">Compliance Level</Label>
              <Select
                value={formData.level}
                onValueChange={(value) => handleChange('level', value as WcagLevel)}
                disabled={disabled || isLoading}
              >
                <SelectTrigger>
                  <SelectValue placeholder="Select compliance level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="A">Level A (Minimum)</SelectItem>
                  <SelectItem value="AA">Level AA (Standard)</SelectItem>
                  <SelectItem value="AAA">Level AAA (Enhanced)</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Scan Options */}
          <div className="space-y-4">
            <Label className="text-base font-medium">Scan Options</Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableContrastAnalysis"
                  checked={formData.enableContrastAnalysis}
                  onCheckedChange={(checked) => handleChange('enableContrastAnalysis', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableContrastAnalysis" className="text-sm font-normal">
                  Color Contrast Analysis
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableKeyboardTesting"
                  checked={formData.enableKeyboardTesting}
                  onCheckedChange={(checked) => handleChange('enableKeyboardTesting', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableKeyboardTesting" className="text-sm font-normal">
                  Keyboard Accessibility Testing
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableFocusAnalysis"
                  checked={formData.enableFocusAnalysis}
                  onCheckedChange={(checked) => handleChange('enableFocusAnalysis', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableFocusAnalysis" className="text-sm font-normal">
                  Focus Management Analysis
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="enableSemanticValidation"
                  checked={formData.enableSemanticValidation}
                  onCheckedChange={(checked) => handleChange('enableSemanticValidation', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableSemanticValidation" className="text-sm font-normal">
                  Semantic Structure Validation
                </Label>
              </div>
            </div>

            {/* Manual Review Option */}
            <div className="mt-4 p-4 border rounded-lg bg-blue-50 dark:bg-blue-900/20">
              <div className="flex items-center space-x-2 mb-2">
                <Checkbox
                  id="enableManualReview"
                  checked={formData.enableManualReview}
                  onCheckedChange={(checked) => handleChange('enableManualReview', checked)}
                  disabled={disabled || isLoading}
                />
                <Label htmlFor="enableManualReview" className="text-sm font-medium">
                  Enable Manual Review Items
                </Label>
              </div>
              <p className="text-xs text-muted-foreground ml-6">
                Generate manual review items for accessibility checks that require human evaluation.
                These can be reviewed later in the Manual Review section.
              </p>
            </div>
          </div>

          {/* Advanced Options */}
          <Collapsible open={showAdvanced} onOpenChange={setShowAdvanced}>
            <CollapsibleTrigger asChild>
              <Button
                type="button"
                variant="outline"
                className="w-full justify-between"
                disabled={disabled || isLoading}
              >
                <span className="flex items-center gap-2">
                  <Settings className="h-4 w-4" />
                  {showAdvanced ? 'Hide' : 'Show'} Advanced Options
                </span>
              </Button>
            </CollapsibleTrigger>
            <CollapsibleContent className="space-y-4 pt-4">
              <div className="space-y-2">
                <Label htmlFor="maxPages">Maximum Pages to Scan: {formData.maxPages}</Label>
                <div className="px-3">
                  <input
                    type="range"
                    id="maxPages"
                    min="1"
                    max="10"
                    value={formData.maxPages}
                    onChange={(e) => handleChange('maxPages', parseInt(e.target.value))}
                    disabled={disabled || isLoading}
                    className="w-full h-2 bg-gray-200 rounded-lg appearance-none cursor-pointer"
                  />
                  <div className="flex justify-between text-xs text-muted-foreground mt-1">
                    <span>1</span>
                    <span>5</span>
                    <span>10</span>
                  </div>
                </div>
                <p className="text-sm text-muted-foreground">
                  Scanning more pages provides comprehensive results but takes longer
                </p>
              </div>
            </CollapsibleContent>
          </Collapsible>

          {/* Action Buttons */}
          <div className="flex flex-col sm:flex-row gap-3 pt-4">
            <Button
              type="button"
              variant="outline"
              onClick={handleReset}
              disabled={disabled || isLoading}
              className="flex items-center gap-2"
            >
              <RotateCcw className="h-4 w-4" />
              Reset
            </Button>

            <Button
              type="submit"
              disabled={disabled || isLoading || !!urlError || !formData.targetUrl}
              className="flex items-center gap-2 flex-1"
            >
              {isLoading ? (
                <>
                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                  Starting Scan...
                </>
              ) : (
                <>
                  <Play className="h-4 w-4" />
                  Start Scan
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
};

export default WcagScanForm;
