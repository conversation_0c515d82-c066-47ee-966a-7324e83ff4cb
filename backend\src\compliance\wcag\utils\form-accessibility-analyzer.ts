/**
 * Advanced Form Accessibility Analyzer
 * Comprehensive form validation including complex form patterns, validation messages, and error handling
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface FormFieldAnalysis {
  type: string;
  selector: string;
  label: {
    hasLabel: boolean;
    labelText?: string;
    labelMethod: 'explicit' | 'implicit' | 'aria-label' | 'aria-labelledby' | 'placeholder' | 'none';
    isAccessible: boolean;
  };
  validation: {
    isRequired: boolean;
    hasValidation: boolean;
    validationMethod: string[];
    errorMessages: string[];
    hasAccessibleErrors: boolean;
  };
  grouping: {
    isInFieldset: boolean;
    fieldsetLegend?: string;
    isInGroup: boolean;
    groupLabel?: string;
  };
  autocomplete: {
    hasAutocomplete: boolean;
    autocompleteValue?: string;
    isValidAutocomplete: boolean;
  };
  accessibility: {
    isKeyboardAccessible: boolean;
    hasProperFocus: boolean;
    hasDescription: boolean;
    ariaAttributes: string[];
  };
  issues: string[];
  recommendations: string[];
  score: number;
}

export interface FormAnalysis {
  selector: string;
  hasAccessibleName: boolean;
  formName?: string;
  fields: FormFieldAnalysis[];
  submitButton: {
    exists: boolean;
    isAccessible: boolean;
    text?: string;
  };
  errorHandling: {
    hasErrorSummary: boolean;
    hasLiveRegion: boolean;
    errorsLinkedToFields: boolean;
  };
  progressIndicator: {
    hasProgress: boolean;
    isAccessible: boolean;
  };
  overallScore: number;
  issues: string[];
  recommendations: string[];
}

export interface FormAccessibilityReport {
  totalForms: number;
  totalFields: number;
  accessibleForms: number;
  accessibleFields: number;
  forms: FormAnalysis[];
  commonIssues: string[];
  bestPractices: string[];
  overallScore: number;
  criticalIssues: string[];
  recommendations: string[];
}

export interface FormAnalysisConfig {
  analyzeLabels: boolean;
  analyzeValidation: boolean;
  analyzeGrouping: boolean;
  analyzeAutocomplete: boolean;
  analyzeErrorHandling: boolean;
  analyzeKeyboardAccess: boolean;
  includeHiddenFields: boolean;
  strictMode: boolean;
}

/**
 * Advanced form accessibility analyzer
 */
export class FormAccessibilityAnalyzer {
  private static instance: FormAccessibilityAnalyzer;

  private constructor() {}

  static getInstance(): FormAccessibilityAnalyzer {
    if (!FormAccessibilityAnalyzer.instance) {
      FormAccessibilityAnalyzer.instance = new FormAccessibilityAnalyzer();
    }
    return FormAccessibilityAnalyzer.instance;
  }

  /**
   * Analyze form accessibility
   */
  async analyzeFormAccessibility(page: Page, config: Partial<FormAnalysisConfig> = {}): Promise<FormAccessibilityReport> {
    const fullConfig: FormAnalysisConfig = {
      analyzeLabels: config.analyzeLabels ?? true,
      analyzeValidation: config.analyzeValidation ?? true,
      analyzeGrouping: config.analyzeGrouping ?? true,
      analyzeAutocomplete: config.analyzeAutocomplete ?? true,
      analyzeErrorHandling: config.analyzeErrorHandling ?? true,
      analyzeKeyboardAccess: config.analyzeKeyboardAccess ?? true,
      includeHiddenFields: config.includeHiddenFields ?? false,
      strictMode: config.strictMode ?? false,
    };

    logger.debug('📝 Starting advanced form accessibility analysis');

    // Inject form analysis functions
    await this.injectFormAnalysisFunctions(page);

    // Get all forms
    const forms = await this.getForms(page, fullConfig);

    // Analyze each form
    const formAnalyses: FormAnalysis[] = [];
    for (const form of forms) {
      try {
        const analysis = await this.analyzeForm(page, form, fullConfig);
        formAnalyses.push(analysis);
      } catch (error) {
        logger.warn(`Error analyzing form ${form.selector}`, { error });
      }
    }

    // Generate comprehensive report
    const report = this.generateReport(formAnalyses);

    logger.info(`✅ Form accessibility analysis completed`, {
      totalForms: report.totalForms,
      accessibleForms: report.accessibleForms,
      totalFields: report.totalFields,
      overallScore: report.overallScore,
    });

    return report;
  }

  /**
   * Inject form analysis functions into the page
   */
  private async injectFormAnalysisFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as any).formAnalysis = {
        
        /**
         * Get element selector
         */
        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;
          
          const path = [];
          let current = element;
          
          while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
            let selector = current.nodeName.toLowerCase();
            
            if (current.className) {
              const classes = current.className.split(' ').filter(c => c.trim());
              if (classes.length > 0) {
                selector += '.' + classes.slice(0, 2).join('.');
              }
            }
            
            path.unshift(selector);
            current = current.parentElement!;
            
            if (path.length > 4) break;
          }
          
          return path.join(' > ');
        },

        /**
         * Check if element is visible
         */
        isVisible(element: HTMLElement): boolean {
          const style = window.getComputedStyle(element);
          return style.display !== 'none' && 
                 style.visibility !== 'hidden' && 
                 style.opacity !== '0';
        },

        /**
         * Analyze form field label
         */
        analyzeFieldLabel(field: HTMLElement): any {
          const issues: string[] = [];
          let labelText = '';
          let labelMethod = 'none';
          let isAccessible = false;

          // Check for explicit label
          if (field.id) {
            const explicitLabel = document.querySelector(`label[for="${field.id}"]`);
            if (explicitLabel) {
              labelText = explicitLabel.textContent?.trim() || '';
              labelMethod = 'explicit';
              isAccessible = labelText.length > 0;
            }
          }

          // Check for implicit label (field inside label)
          if (!isAccessible) {
            const implicitLabel = field.closest('label');
            if (implicitLabel) {
              labelText = implicitLabel.textContent?.trim() || '';
              labelMethod = 'implicit';
              isAccessible = labelText.length > 0;
            }
          }

          // Check for aria-label
          if (!isAccessible) {
            const ariaLabel = field.getAttribute('aria-label');
            if (ariaLabel) {
              labelText = ariaLabel.trim();
              labelMethod = 'aria-label';
              isAccessible = labelText.length > 0;
            }
          }

          // Check for aria-labelledby
          if (!isAccessible) {
            const ariaLabelledby = field.getAttribute('aria-labelledby');
            if (ariaLabelledby) {
              const labelElement = document.getElementById(ariaLabelledby);
              if (labelElement) {
                labelText = labelElement.textContent?.trim() || '';
                labelMethod = 'aria-labelledby';
                isAccessible = labelText.length > 0;
              }
            }
          }

          // Check for placeholder (not recommended as primary label)
          if (!isAccessible) {
            const placeholder = field.getAttribute('placeholder');
            if (placeholder) {
              labelText = placeholder.trim();
              labelMethod = 'placeholder';
              isAccessible = false; // Placeholder alone is not accessible
              issues.push('Using placeholder as primary label is not accessible');
            }
          }

          if (!isAccessible) {
            issues.push('Field lacks accessible label');
          }

          return {
            hasLabel: labelMethod !== 'none',
            labelText: labelText || undefined,
            labelMethod,
            isAccessible,
            issues,
          };
        },

        /**
         * Analyze field validation
         */
        analyzeFieldValidation(field: HTMLElement): any {
          const issues: string[] = [];
          const validationMethod: string[] = [];
          const errorMessages: string[] = [];

          const isRequired = field.hasAttribute('required') || 
                           field.getAttribute('aria-required') === 'true';

          if (isRequired) {
            validationMethod.push('required');
          }

          // Check for HTML5 validation
          const inputElement = field as HTMLInputElement;
          if (inputElement.pattern) {
            validationMethod.push('pattern');
          }
          if (inputElement.min || inputElement.max) {
            validationMethod.push('min-max');
          }
          if (inputElement.minLength || inputElement.maxLength) {
            validationMethod.push('length');
          }

          // Check for ARIA validation
          if (field.getAttribute('aria-invalid')) {
            validationMethod.push('aria-invalid');
          }

          // Check for error messages
          const ariaDescribedby = field.getAttribute('aria-describedby');
          if (ariaDescribedby) {
            const errorElement = document.getElementById(ariaDescribedby);
            if (errorElement) {
              const errorText = errorElement.textContent?.trim();
              if (errorText) {
                errorMessages.push(errorText);
              }
            }
          }

          // Look for nearby error messages
          const fieldContainer = field.closest('.field, .form-group, .input-group') || field.parentElement;
          if (fieldContainer) {
            const errorElements = fieldContainer.querySelectorAll('.error, .invalid, [role="alert"]');
            errorElements.forEach(errorEl => {
              const errorText = errorEl.textContent?.trim();
              if (errorText && !errorMessages.includes(errorText)) {
                errorMessages.push(errorText);
              }
            });
          }

          const hasValidation = validationMethod.length > 0;
          const hasAccessibleErrors = errorMessages.length > 0 && ariaDescribedby !== null;

          if (isRequired && !hasAccessibleErrors && field.getAttribute('aria-invalid') === 'true') {
            issues.push('Required field with errors lacks accessible error message');
          }

          return {
            isRequired,
            hasValidation,
            validationMethod,
            errorMessages,
            hasAccessibleErrors,
            issues,
          };
        },

        /**
         * Analyze field grouping
         */
        analyzeFieldGrouping(field: HTMLElement): any {
          const fieldset = field.closest('fieldset');
          const isInFieldset = !!fieldset;
          let fieldsetLegend = '';

          if (fieldset) {
            const legend = fieldset.querySelector('legend');
            fieldsetLegend = legend?.textContent?.trim() || '';
          }

          // Check for role="group"
          const group = field.closest('[role="group"]');
          const isInGroup = !!group;
          let groupLabel = '';

          if (group) {
            groupLabel = group.getAttribute('aria-label') || 
                        (group.getAttribute('aria-labelledby') && 
                         document.getElementById(group.getAttribute('aria-labelledby')!)?.textContent) || '';
          }

          return {
            isInFieldset,
            fieldsetLegend: fieldsetLegend || undefined,
            isInGroup,
            groupLabel: groupLabel || undefined,
          };
        },

        /**
         * Analyze autocomplete
         */
        analyzeAutocomplete(field: HTMLElement): any {
          const autocompleteValue = field.getAttribute('autocomplete');
          const hasAutocomplete = !!autocompleteValue;

          // Valid autocomplete values (simplified list)
          const validValues = [
            'name', 'given-name', 'family-name', 'email', 'username', 'new-password',
            'current-password', 'tel', 'address-line1', 'address-line2', 'country',
            'postal-code', 'cc-name', 'cc-number', 'cc-exp', 'cc-csc', 'off'
          ];

          const isValidAutocomplete = !hasAutocomplete || 
                                    validValues.includes(autocompleteValue!) ||
                                    autocompleteValue === 'off';

          return {
            hasAutocomplete,
            autocompleteValue: autocompleteValue || undefined,
            isValidAutocomplete,
          };
        },

        /**
         * Analyze accessibility features
         */
        analyzeAccessibility(field: HTMLElement): any {
          const issues: string[] = [];
          const ariaAttributes: string[] = [];

          // Check keyboard accessibility
          const isKeyboardAccessible = field.tabIndex >= 0 || 
                                      ['INPUT', 'SELECT', 'TEXTAREA', 'BUTTON'].includes(field.tagName);

          // Check focus indicator
          field.focus();
          const style = window.getComputedStyle(field, ':focus');
          const hasProperFocus = style.outline !== 'none' && style.outline !== '0px';

          if (!hasProperFocus) {
            issues.push('Field lacks visible focus indicator');
          }

          // Check for description
          const hasDescription = !!(field.getAttribute('aria-describedby') || 
                                   field.getAttribute('title'));

          // Collect ARIA attributes
          Array.from(field.attributes).forEach(attr => {
            if (attr.name.startsWith('aria-')) {
              ariaAttributes.push(attr.name);
            }
          });

          return {
            isKeyboardAccessible,
            hasProperFocus,
            hasDescription,
            ariaAttributes,
            issues,
          };
        },

        /**
         * Get all form fields
         */
        getFormFields(form: HTMLFormElement, includeHidden: boolean): HTMLElement[] {
          const fieldSelectors = [
            'input:not([type="hidden"])',
            'select',
            'textarea',
            'button[type="submit"]',
            '[role="textbox"]',
            '[role="combobox"]',
            '[role="listbox"]',
            '[role="checkbox"]',
            '[role="radio"]'
          ];

          if (includeHidden) {
            fieldSelectors.push('input[type="hidden"]');
          }

          const fields: HTMLElement[] = [];
          fieldSelectors.forEach(selector => {
            const elements = form.querySelectorAll(selector);
            elements.forEach(el => {
              const element = el as HTMLElement;
              if (includeHidden || this.isVisible(element)) {
                fields.push(element);
              }
            });
          });

          return fields;
        },

        /**
         * Analyze form error handling
         */
        analyzeErrorHandling(form: HTMLFormElement): any {
          // Check for error summary
          const errorSummary = form.querySelector('.error-summary, [role="alert"], .alert-error');
          const hasErrorSummary = !!errorSummary;

          // Check for live region
          const liveRegion = form.querySelector('[aria-live]');
          const hasLiveRegion = !!liveRegion;

          // Check if errors are linked to fields
          const errorElements = form.querySelectorAll('.error, .invalid, [role="alert"]');
          let errorsLinkedToFields = true;

          errorElements.forEach(errorEl => {
            const errorElement = errorEl as HTMLElement;
            const errorId = errorElement.id;
            
            if (errorId) {
              const linkedField = form.querySelector(`[aria-describedby*="${errorId}"]`);
              if (!linkedField) {
                errorsLinkedToFields = false;
              }
            } else {
              errorsLinkedToFields = false;
            }
          });

          return {
            hasErrorSummary,
            hasLiveRegion,
            errorsLinkedToFields,
          };
        },

        /**
         * Analyze progress indicator
         */
        analyzeProgressIndicator(form: HTMLFormElement): any {
          const progressElements = form.querySelectorAll('[role="progressbar"], .progress, .step-indicator');
          const hasProgress = progressElements.length > 0;

          let isAccessible = false;
          if (hasProgress) {
            progressElements.forEach(progress => {
              const progressElement = progress as HTMLElement;
              const hasLabel = !!(progressElement.getAttribute('aria-label') || 
                                progressElement.getAttribute('aria-labelledby'));
              const hasValue = !!(progressElement.getAttribute('aria-valuenow') || 
                                progressElement.getAttribute('aria-valuetext'));
              
              if (hasLabel && hasValue) {
                isAccessible = true;
              }
            });
          }

          return {
            hasProgress,
            isAccessible,
          };
        }
      };
    });
  }

  /**
   * Get all forms from the page
   */
  private async getForms(page: Page, config: FormAnalysisConfig): Promise<{ selector: string; element: any }[]> {
    return await page.evaluate(() => {
      const forms: { selector: string; element: any }[] = [];
      const formElements = document.querySelectorAll('form');
      const formAnalysis = (window as any).formAnalysis;
      
      formElements.forEach(form => {
        forms.push({
          selector: formAnalysis.getElementSelector(form),
          element: {
            tagName: form.tagName,
            action: form.action,
            method: form.method,
          },
        });
      });
      
      return forms;
    });
  }

  /**
   * Analyze individual form
   */
  private async analyzeForm(page: Page, formInfo: any, config: FormAnalysisConfig): Promise<FormAnalysis> {
    return await page.evaluate((selector, analysisConfig) => {
      const formAnalysis = (window as any).formAnalysis;
      const form = document.querySelector(selector) as HTMLFormElement;
      
      if (!form) {
        throw new Error(`Form not found: ${selector}`);
      }

      const issues: string[] = [];
      const recommendations: string[] = [];

      // Analyze form name/label
      const hasAccessibleName = !!(
        form.getAttribute('aria-label') ||
        form.getAttribute('aria-labelledby') ||
        form.querySelector('h1, h2, h3, h4, h5, h6')
      );

      const formName = form.getAttribute('aria-label') ||
                      (form.getAttribute('aria-labelledby') && 
                       document.getElementById(form.getAttribute('aria-labelledby')!)?.textContent) ||
                      form.querySelector('h1, h2, h3, h4, h5, h6')?.textContent;

      if (!hasAccessibleName) {
        issues.push('Form lacks accessible name');
        recommendations.push('Add aria-label or associate with heading');
      }

      // Get and analyze fields
      const fieldElements = formAnalysis.getFormFields(form, analysisConfig.includeHiddenFields);
      const fields: any[] = [];

      fieldElements.forEach((field: HTMLElement) => {
        const fieldIssues: string[] = [];
        const fieldRecommendations: string[] = [];

        // Analyze label
        const labelAnalysis = analysisConfig.analyzeLabels ? 
          formAnalysis.analyzeFieldLabel(field) : { hasLabel: true, isAccessible: true, issues: [] };
        fieldIssues.push(...labelAnalysis.issues);

        // Analyze validation
        const validationAnalysis = analysisConfig.analyzeValidation ? 
          formAnalysis.analyzeFieldValidation(field) : { isRequired: false, hasValidation: false, hasAccessibleErrors: true, issues: [] };
        fieldIssues.push(...validationAnalysis.issues);

        // Analyze grouping
        const groupingAnalysis = analysisConfig.analyzeGrouping ? 
          formAnalysis.analyzeFieldGrouping(field) : { isInFieldset: false, isInGroup: false };

        // Analyze autocomplete
        const autocompleteAnalysis = analysisConfig.analyzeAutocomplete ? 
          formAnalysis.analyzeAutocomplete(field) : { hasAutocomplete: false, isValidAutocomplete: true };

        // Analyze accessibility
        const accessibilityAnalysis = analysisConfig.analyzeKeyboardAccess ? 
          formAnalysis.analyzeAccessibility(field) : { isKeyboardAccessible: true, hasProperFocus: true, hasDescription: false, ariaAttributes: [], issues: [] };
        fieldIssues.push(...accessibilityAnalysis.issues);

        // Calculate field score
        let fieldScore = 100;
        if (!labelAnalysis.isAccessible) fieldScore -= 30;
        if (validationAnalysis.isRequired && !validationAnalysis.hasAccessibleErrors) fieldScore -= 25;
        if (!accessibilityAnalysis.isKeyboardAccessible) fieldScore -= 20;
        if (!accessibilityAnalysis.hasProperFocus) fieldScore -= 15;
        fieldScore -= fieldIssues.length * 5;
        fieldScore = Math.max(0, fieldScore);

        // Generate recommendations
        if (!labelAnalysis.isAccessible) {
          fieldRecommendations.push('Add accessible label to field');
        }
        if (validationAnalysis.isRequired && !validationAnalysis.hasAccessibleErrors) {
          fieldRecommendations.push('Link error messages to field with aria-describedby');
        }
        if (!accessibilityAnalysis.hasProperFocus) {
          fieldRecommendations.push('Add visible focus indicator');
        }

        fields.push({
          type: field.tagName.toLowerCase(),
          selector: formAnalysis.getElementSelector(field),
          label: labelAnalysis,
          validation: validationAnalysis,
          grouping: groupingAnalysis,
          autocomplete: autocompleteAnalysis,
          accessibility: accessibilityAnalysis,
          issues: fieldIssues,
          recommendations: fieldRecommendations,
          score: fieldScore,
        });
      });

      // Analyze submit button
      const submitButton = form.querySelector('button[type="submit"], input[type="submit"]') as HTMLElement;
      const submitAnalysis = {
        exists: !!submitButton,
        isAccessible: false,
        text: undefined as string | undefined,
      };

      if (submitButton) {
        const buttonText = submitButton.textContent?.trim() || 
                          submitButton.getAttribute('value') || 
                          submitButton.getAttribute('aria-label');
        submitAnalysis.text = buttonText || undefined;
        submitAnalysis.isAccessible = !!buttonText;

        if (!submitAnalysis.isAccessible) {
          issues.push('Submit button lacks accessible text');
          recommendations.push('Add text content or aria-label to submit button');
        }
      } else {
        issues.push('Form lacks submit button');
        recommendations.push('Add submit button to form');
      }

      // Analyze error handling
      const errorHandling = analysisConfig.analyzeErrorHandling ? 
        formAnalysis.analyzeErrorHandling(form) : { hasErrorSummary: false, hasLiveRegion: false, errorsLinkedToFields: true };

      // Analyze progress indicator
      const progressIndicator = formAnalysis.analyzeProgressIndicator(form);

      // Calculate overall form score
      const fieldScores = fields.map(f => f.score);
      const averageFieldScore = fieldScores.length > 0 ? 
        fieldScores.reduce((sum, score) => sum + score, 0) / fieldScores.length : 100;

      let overallScore = averageFieldScore;
      if (!hasAccessibleName) overallScore -= 15;
      if (!submitAnalysis.isAccessible) overallScore -= 10;
      if (!errorHandling.errorsLinkedToFields) overallScore -= 15;
      overallScore = Math.max(0, overallScore);

      return {
        selector,
        hasAccessibleName,
        formName,
        fields,
        submitButton: submitAnalysis,
        errorHandling,
        progressIndicator,
        overallScore: Math.round(overallScore),
        issues,
        recommendations,
      };
    }, formInfo.selector, config);
  }

  /**
   * Generate comprehensive report
   */
  private generateReport(forms: FormAnalysis[]): FormAccessibilityReport {
    const totalForms = forms.length;
    const totalFields = forms.reduce((sum, form) => sum + form.fields.length, 0);
    const accessibleForms = forms.filter(form => form.overallScore >= 80).length;
    const accessibleFields = forms.reduce((sum, form) => 
      sum + form.fields.filter(field => field.score >= 80).length, 0
    );

    // Calculate overall score
    const formScores = forms.map(form => form.overallScore);
    const overallScore = formScores.length > 0 ? 
      Math.round(formScores.reduce((sum, score) => sum + score, 0) / formScores.length) : 100;

    // Collect common issues
    const allIssues: string[] = [];
    forms.forEach(form => {
      allIssues.push(...form.issues);
      form.fields.forEach(field => {
        allIssues.push(...field.issues);
      });
    });

    const issueCounts: { [key: string]: number } = {};
    allIssues.forEach(issue => {
      issueCounts[issue] = (issueCounts[issue] || 0) + 1;
    });

    const commonIssues = Object.entries(issueCounts)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 5)
      .map(([issue]) => issue);

    // Generate critical issues
    const criticalIssues: string[] = [];
    if (accessibleForms / totalForms < 0.5) {
      criticalIssues.push('Less than 50% of forms are accessible');
    }
    if (accessibleFields / totalFields < 0.7) {
      criticalIssues.push('Less than 70% of form fields are accessible');
    }

    // Generate recommendations
    const recommendations: string[] = [
      'Ensure all form fields have accessible labels',
      'Link error messages to fields using aria-describedby',
      'Add visible focus indicators to all form controls',
      'Use fieldsets and legends for related form controls',
      'Implement proper error handling and validation',
    ];

    // Best practices
    const bestPractices: string[] = [
      'Use semantic HTML form elements',
      'Provide clear instructions and help text',
      'Use autocomplete attributes for common fields',
      'Test forms with keyboard navigation',
      'Validate forms both client-side and server-side',
    ];

    return {
      totalForms,
      totalFields,
      accessibleForms,
      accessibleFields,
      forms,
      commonIssues,
      bestPractices,
      overallScore,
      criticalIssues,
      recommendations,
    };
  }
}

export default FormAccessibilityAnalyzer;
