/**
 * WCAG Rule 11: Focus Not Obscured (Enhanced) - 2.4.12
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
// import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { FocusTracker } from '../utils/focus-tracker';
import { WcagEvidence } from '../types';

export class FocusNotObscuredEnhancedCheck {
  private checkTemplate = new CheckTemplate();

  /**
   * Perform focus not obscured enhanced check - 100% automated
   */
  async performCheck(config: CheckConfig) {
    return this.checkTemplate.executeCheck(
      'WCAG-011',
      'Focus Not Obscured (Enhanced)',
      'operable',
      0.04,
      'AAA',
      config,
      this.executeFocusNotObscuredEnhancedCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );
  }

  /**
   * Execute enhanced focus obstruction analysis (stricter than minimum)
   */
  private async executeFocusNotObscuredEnhancedCheck(page: Page, _config: CheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Get all focusable elements
    const focusableElements = await FocusTracker.getFocusableElements(page);

    if (!focusableElements || focusableElements.length === 0) {
      evidence.push({
        type: 'text',
        description: 'No focusable elements found on page',
        value: 'Page may not have interactive content or elements are not properly marked as focusable',
        severity: 'warning',
      });

      return {
        score: 100, // No focusable elements means no focus obstruction issues
        maxScore: 100,
        evidence,
        issues: [],
        recommendations: ['Ensure interactive elements are properly focusable'],
      };
    }

    let totalElements = 0;
    let passedElements = 0;

    // Test each focusable element for any obstruction (enhanced requirements)
    for (const element of focusableElements) {
      totalElements++;

      try {
        // Check if element still exists before trying to focus
        const elementExists = await page.evaluate((selector) => {
          try {
            return document.querySelector(selector) !== null;
          } catch {
            return false;
          }
        }, element.selector);

        if (!elementExists) {
          issues.push(`Element ${element.selector} no longer exists on page`);
          continue;
        }

        // Focus the element with error handling
        await page.focus(element.selector);

        // Enhanced check: element must be completely unobscured
        const isCompletelyVisible = await this.checkCompleteVisibility(page, element.selector);

        if (isCompletelyVisible.fullyVisible) {
          passedElements++;

          evidence.push({
            type: 'measurement',
            description: 'Focused element is completely visible (enhanced requirement)',
            value: 'Element is 100% visible when focused',
            selector: element.selector,
            severity: 'info',
          });
        } else {
          issues.push(
            `Focused element ${element.selector} has partial obstruction: ${isCompletelyVisible.obstructionDetails}`,
          );

          evidence.push({
            type: 'measurement',
            description: 'Focused element has partial obstruction (fails enhanced requirement)',
            value: isCompletelyVisible.obstructionDetails,
            selector: element.selector,
            severity: 'error',
          });

          recommendations.push(
            `Ensure ${element.selector} is completely visible when focused (AAA requirement)`,
          );
        }
      } catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        issues.push(`Failed to test element ${element.selector}: ${errorMessage}`);

        evidence.push({
          type: 'text',
          description: 'Element focus test failed',
          value: `Could not focus element ${element.selector}: ${errorMessage}`,
          selector: element.selector,
          severity: 'warning',
        });
      }
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Enhanced focus visibility analysis summary',
      value: `${passedElements}/${totalElements} focused elements are completely visible (AAA standard)`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error',
    });

    if (score < 100) {
      recommendations.unshift(
        'Ensure no part of focused elements is ever hidden (AAA requirement)',
      );
      recommendations.push('Consider redesigning layout to avoid any focus obstruction');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Check if element is completely visible (enhanced requirement)
   */
  private async checkCompleteVisibility(
    page: Page,
    selector: string,
  ): Promise<{
    fullyVisible: boolean;
    obstructionDetails: string;
  }> {
    return await page.evaluate((sel) => {
      const element = document.querySelector(sel) as HTMLElement;
      if (!element) {
        return { fullyVisible: false, obstructionDetails: 'Element not found' };
      }

      const rect = element.getBoundingClientRect();
      const obstructions: string[] = [];

      // Check multiple points across the element
      const checkPoints = [
        { x: rect.left + 5, y: rect.top + 5 }, // Top-left
        { x: rect.right - 5, y: rect.top + 5 }, // Top-right
        { x: rect.left + 5, y: rect.bottom - 5 }, // Bottom-left
        { x: rect.right - 5, y: rect.bottom - 5 }, // Bottom-right
        { x: rect.left + rect.width / 2, y: rect.top + rect.height / 2 }, // Center
      ];

      checkPoints.forEach((point, index) => {
        const elementAtPoint = document.elementFromPoint(point.x, point.y);
        if (elementAtPoint && !element.contains(elementAtPoint)) {
          const obstructingElement = elementAtPoint as HTMLElement;
          const style = window.getComputedStyle(obstructingElement);

          if (style.position === 'fixed' || style.position === 'sticky') {
            obstructions.push(
              `Point ${index + 1} obscured by ${obstructingElement.tagName.toLowerCase()}`,
            );
          }
        }
      });

      return {
        fullyVisible: obstructions.length === 0,
        obstructionDetails:
          obstructions.length > 0 ? obstructions.join('; ') : 'Element is completely visible',
      };
    }, selector);
  }
}
