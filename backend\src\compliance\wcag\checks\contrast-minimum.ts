/**
 * WCAG Rule 4: Contrast (Minimum) - 1.4.3
 * 100% Automated - No manual review required
 */

import { Page } from 'puppeteer';
import { CheckConfig, CheckTemplate } from '../utils/check-template';
import { ColorAnalyzer } from '../utils/color-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import SmartCache from '../utils/smart-cache';
import { WcagEvidence } from '../types';

export interface ContrastCheckConfig extends CheckConfig {
  includeImages?: boolean;
  checkAllElements?: boolean;
  useEnhancedAnalysis?: boolean;
  enableGradientDetection?: boolean;
  enableCustomPropertyResolution?: boolean;
}

export class ContrastMinimumCheck {
  private checkTemplate = new CheckTemplate();
  private enhancedAnalyzer = EnhancedColorAnalyzer.getInstance();
  private smartCache = SmartCache.getInstance();

  /**
   * Perform contrast minimum check - 100% automated
   */
  async performCheck(config: ContrastCheckConfig) {
    return this.checkTemplate.executeCheck(
      'WCAG-004',
      'Contrast (Minimum)',
      'perceivable',
      0.1,
      'AA',
      config,
      this.executeContrastCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );
  }

  /**
   * Execute enhanced contrast analysis on all text elements
   */
  private async executeContrastCheck(page: Page, config: ContrastCheckConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];

    // Check if enhanced analysis is enabled (default: true)
    const useEnhanced = config.useEnhancedAnalysis !== false;

    if (useEnhanced) {
      return this.executeEnhancedContrastCheck(page, config, evidence, issues, recommendations);
    }

    // Fallback to legacy analysis
    return this.executeLegacyContrastCheck(page, config, evidence, issues, recommendations);
  }

  /**
   * Execute enhanced contrast analysis with gradient and CSS custom property support
   */
  private async executeEnhancedContrastCheck(
    page: Page,
    config: ContrastCheckConfig,
    evidence: WcagEvidence[],
    issues: string[],
    recommendations: string[]
  ) {
    try {
      // Check cache first
      const cacheKey = `enhanced-contrast:${config.targetUrl}`;
      const cached = await this.smartCache.getSiteAnalysis(config.targetUrl, 'enhanced-contrast');

      let enhancedResults;
      if (cached) {
        enhancedResults = cached;
        evidence.push({
          type: 'info',
          description: 'Enhanced contrast analysis (cached)',
          value: 'Using cached analysis results',
          severity: 'info',
        });
      } else {
        // Perform enhanced analysis
        enhancedResults = await this.enhancedAnalyzer.analyzePageContrast(page);

        // Cache results
        await this.smartCache.cacheSiteAnalysis(
          config.targetUrl,
          'enhanced-contrast',
          enhancedResults,
          3600000 // 1 hour TTL
        );
      }

      let totalElements = 0;
      let passedElements = 0;
      let gradientElements = 0;
      let imageBackgroundElements = 0;
      let customPropertyElements = 0;

      // Process enhanced results
      const resultsArray = Array.isArray(enhancedResults) ? enhancedResults : [];
      for (const result of resultsArray) {
        if (!result.text.text || result.text.text.length < 3) continue;

        totalElements++;

        // Track advanced features detected
        if (result.background.type === 'gradient') {
          gradientElements++;
        }
        if (result.background.type === 'image') {
          imageBackgroundElements++;
        }
        if (Object.keys(result.cssCustomProperties).length > 0) {
          customPropertyElements++;
        }

        const contrastRatio = result.contrast.ratio;
        const passes = result.accessibility.isAccessible;

        if (passes) {
          passedElements++;
        } else {
          issues.push(
            `Low contrast ratio (${contrastRatio}:1) for text "${result.text.text.substring(0, 50)}..." at ${result.element}`
          );
        }

        // Add detailed evidence
        evidence.push({
          type: 'text',
          description: `Enhanced contrast analysis: ${result.text.text.substring(0, 30)}...`,
          value: `${contrastRatio}:1 (${result.accessibility.level}) - ${result.background.type} background`,
          severity: passes ? 'info' : 'error',
        });

        // Add specific recommendations for complex backgrounds
        if (result.background.type === 'gradient' && !passes) {
          recommendations.push(`Gradient background detected - consider using solid color overlay for better contrast`);
        }
        if (result.background.type === 'image' && !passes) {
          recommendations.push(`Image background detected - add semi-transparent overlay to improve text readability`);
        }
        if (result.contrast.confidence < 0.7) {
          recommendations.push(`Complex background detected - manual verification recommended for "${result.text.text.substring(0, 30)}..."`);
        }
      }

      // Add enhanced analysis summary
      evidence.unshift({
        type: 'info',
        description: 'Enhanced contrast analysis summary',
        value: `${passedElements}/${totalElements} elements pass. Features: ${gradientElements} gradients, ${imageBackgroundElements} images, ${customPropertyElements} custom properties`,
        severity: 'info',
      });

      // Calculate score
      const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

      // Add enhanced recommendations
      if (gradientElements > 0) {
        recommendations.push('Gradient backgrounds detected - ensure sufficient contrast across all gradient stops');
      }
      if (imageBackgroundElements > 0) {
        recommendations.push('Image backgrounds detected - consider adding text shadows or background overlays');
      }
      if (customPropertyElements > 0) {
        recommendations.push('CSS custom properties detected - verify contrast in all theme variations');
      }

      return {
        score,
        maxScore: 100,
        evidence,
        issues,
        recommendations,
      };

    } catch (error) {
      // Fallback to legacy analysis on error
      evidence.push({
        type: 'warning',
        description: 'Enhanced analysis failed, using legacy method',
        value: error instanceof Error ? error.message : 'Unknown error',
        severity: 'warning',
      });

      return this.executeLegacyContrastCheck(page, config, evidence, issues, recommendations);
    }
  }

  /**
   * Execute legacy contrast analysis (fallback)
   */
  private async executeLegacyContrastCheck(
    page: Page,
    config: ContrastCheckConfig,
    evidence: WcagEvidence[],
    issues: string[],
    recommendations: string[]
  ) {
    // Get all text elements with computed styles
    const textElements = await page.evaluate(() => {
      const elements: Array<{
        selector: string;
        text: string;
        foregroundColor: string;
        backgroundColor: string;
        fontSize: string;
        fontWeight: string;
        tagName: string;
        isVisible: boolean;
      }> = [];

      // Helper function to get effective background color
      function getEffectiveBackgroundColor(element: HTMLElement): string {
        let current = element;

        while (current && current !== document.body) {
          const style = window.getComputedStyle(current);
          const bgColor = style.backgroundColor;

          if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
            return bgColor;
          }
          current = current.parentElement as HTMLElement;
        }

        return '#ffffff'; // Default to white
      }

      // Helper function to generate selector
      function generateSelector(element: HTMLElement, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className.split(' ').filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      // Get all elements with text content
      const allElements = document.querySelectorAll('*');

      allElements.forEach((element, index) => {
        const htmlElement = element as HTMLElement;
        const computedStyle = window.getComputedStyle(htmlElement);

        // Check if element has direct text content (not just child text)
        const hasDirectText = Array.from(htmlElement.childNodes).some(
          (node) => node.nodeType === Node.TEXT_NODE && node.textContent?.trim(),
        );

        if (hasDirectText) {
          const isVisible =
            computedStyle.display !== 'none' &&
            computedStyle.visibility !== 'hidden' &&
            computedStyle.opacity !== '0';

          if (isVisible) {
            elements.push({
              selector: generateSelector(htmlElement, index),
              text: htmlElement.textContent?.trim() || '',
              foregroundColor: computedStyle.color,
              backgroundColor: getEffectiveBackgroundColor(htmlElement),
              fontSize: computedStyle.fontSize,
              fontWeight: computedStyle.fontWeight,
              tagName: htmlElement.tagName.toLowerCase(),
              isVisible,
            });
          }
        }
      });

      return elements;
    });

    let totalElements = 0;
    let passedElements = 0;

    // Analyze contrast for each text element
    for (const element of textElements) {
      if (element.text.length < 3) continue; // Skip very short text

      totalElements++;

      const isLargeText = ColorAnalyzer.isLargeText(element.fontSize, element.fontWeight);
      const contrastResult = ColorAnalyzer.analyzeContrast(
        element.foregroundColor,
        element.backgroundColor,
        isLargeText,
      );

      if (contrastResult.passes) {
        passedElements++;

        evidence.push({
          type: 'measurement',
          description: `Text contrast passes ${contrastResult.level} standards`,
          value: `Contrast ratio: ${contrastResult.ratio}:1 (${isLargeText ? 'large' : 'normal'} text)`,
          selector: element.selector,
          severity: 'info',
        });
      } else {
        issues.push(`Low contrast on ${element.selector}: ${contrastResult.ratio}:1`);

        evidence.push({
          type: 'measurement',
          description: 'Text contrast fails WCAG standards',
          value: `Contrast ratio: ${contrastResult.ratio}:1 (required: ${isLargeText ? '3.0' : '4.5'}:1)`,
          selector: element.selector,
          severity: 'error',
        });

        if (contrastResult.recommendation) {
          recommendations.push(`${element.selector}: ${contrastResult.recommendation}`);
        }
      }
    }

    // Calculate score
    const score = totalElements > 0 ? Math.round((passedElements / totalElements) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Contrast analysis summary',
      value: `${passedElements}/${totalElements} text elements pass contrast requirements`,
      severity: score >= 90 ? 'info' : score >= 70 ? 'warning' : 'error',
    });

    if (score < 100) {
      recommendations.unshift('Review and improve color contrast for better accessibility');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  /**
   * Get effective background color by traversing parent elements
   */
  private getEffectiveBackgroundColor(element: HTMLElement): string {
    let current = element;

    while (current && current !== document.body) {
      const style = window.getComputedStyle(current);
      const bgColor = style.backgroundColor;

      if (bgColor && bgColor !== 'rgba(0, 0, 0, 0)' && bgColor !== 'transparent') {
        return bgColor;
      }
      current = current.parentElement as HTMLElement;
    }

    return '#ffffff'; // Default to white
  }

  /**
   * Generate unique selector for element
   */
  private generateSelector(element: HTMLElement, index: number): string {
    if (element.id) {
      return `#${element.id}`;
    }

    if (element.className) {
      const classes = element.className.split(' ').filter((c) => c.length > 0);
      if (classes.length > 0) {
        return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
      }
    }

    return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
  }
}
