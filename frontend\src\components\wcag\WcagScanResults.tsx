/**
 * Enhanced WCAG Scan Results Component
 * Comprehensive display of scan results with Phase 1-3 enhancements
 */

import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Chip,
  LinearProgress,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  // Grid, // Temporarily disabled due to type issues
  Alert,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Warning as WarningIcon,
  Info as InfoIcon,
  Speed as SpeedIcon,
  Memory as MemoryIcon,
  Psychology as AIIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';

// Simple Grid replacement component to avoid Material-UI type issues
const Grid = ({
  container,
  xs,
  spacing,
  children,
  ...props
}: {
  container?: boolean;
  xs?: number;
  spacing?: number;
  children: React.ReactNode;
  [key: string]: unknown;
}) => {
  if (container) {
    return (
      <div
        style={{
          display: 'grid',
          gap: spacing ? `${spacing * 8}px` : '16px',
          gridTemplateColumns: 'repeat(auto-fit, minmax(300px, 1fr))',
        }}
        {...props}
      >
        {children}
      </div>
    );
  }
  return (
    <div style={{ gridColumn: xs === 12 ? '1 / -1' : 'auto' }} {...props}>
      {children}
    </div>
  );
};

interface WcagScanResult {
  scanId: string;
  url: string;
  timestamp: string;
  status: 'completed' | 'running' | 'failed';
  wcagVersion: '2.1' | '2.2';
  level: 'A' | 'AA' | 'AAA';

  // Core Results
  overallScore: number;
  complianceLevel: string;
  totalChecks: number;
  passedChecks: number;
  failedChecks: number;
  warningChecks: number;

  // Phase 1: Performance Metrics
  performance: {
    scanDuration: number;
    memoryUsage: number;
    cacheHitRate: number;
    concurrentScans: number;
    performanceScore: number;
  };

  // Phase 2: Website Type Analysis
  websiteAnalysis: {
    cms: {
      platform: string;
      confidence: number;
      plugins: string[];
      optimizations: string[];
    };
    ecommerce: {
      platform: string;
      components: string[];
      overallScore: number;
      criticalIssues: string[];
    };
    framework: {
      primaryFramework: string;
      confidence: number;
      libraries: string[];
      optimizations: string[];
    };
    media: {
      totalElements: number;
      accessibleElements: number;
      complianceLevel: string;
      recommendations: string[];
    };
  };

  // Phase 3: VPS Performance
  vpsPerformance: {
    healthScore: number;
    performanceGrade: string;
    resourceUtilization: {
      cpu: number;
      memory: number;
      storage: number;
      network: number;
    };
    optimizationsApplied: string[];
    recommendations: string[];
  };

  // Detailed Results
  ruleResults: Array<{
    ruleId: string;
    ruleName: string;
    level: string;
    status: 'pass' | 'fail' | 'warning' | 'manual';
    description: string;
    impact: 'low' | 'medium' | 'high' | 'critical';
    elements: Array<{
      selector: string;
      message: string;
      recommendation: string;
    }>;
  }>;

  // Enhanced Features
  manualReviewItems: Array<{
    ruleId: string;
    description: string;
    guidance: string;
    priority: 'low' | 'medium' | 'high';
  }>;

  recommendations: string[];
  nextSteps: string[];
}

interface WcagScanResultsProps {
  scanResult: WcagScanResult;
  onRescan?: () => void;
  onExportReport?: () => void;
}

export const WcagScanResults: React.FC<WcagScanResultsProps> = ({
  scanResult,
  onRescan,
  onExportReport,
}) => {
  const [activeTab, setActiveTab] = useState(0);
  // const [expandedAccordion, setExpandedAccordion] = useState<string | false>('overview');

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setActiveTab(newValue);
  };

  // const handleAccordionChange = (panel: string) => (
  //   event: React.SyntheticEvent,
  //   isExpanded: boolean
  // ) => {
  //   setExpandedAccordion(isExpanded ? panel : false);
  // };

  const getScoreColor = (score: number) => {
    if (score >= 90) return 'success';
    if (score >= 70) return 'warning';
    return 'error';
  };

  const getComplianceChip = (level: string) => {
    const colors: { [key: string]: 'success' | 'warning' | 'error' | 'default' } = {
      AAA: 'success',
      AA: 'success',
      A: 'warning',
      fail: 'error',
    };

    return (
      <Chip label={level} color={colors[level] || 'default'} size="small" variant="outlined" />
    );
  };

  const renderOverviewTab = () => (
    <Grid container spacing={3}>
      {/* Overall Score Card */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Overall Score
            </Typography>
            <Box display="flex" alignItems="center" mb={2}>
              <Typography variant="h3" color={getScoreColor(scanResult.overallScore)}>
                {scanResult.overallScore}%
              </Typography>
              <Box ml={2}>{getComplianceChip(scanResult.complianceLevel)}</Box>
            </Box>
            <LinearProgress
              variant="determinate"
              value={scanResult.overallScore}
              color={getScoreColor(scanResult.overallScore)}
              sx={{ height: 8, borderRadius: 4 }}
            />
          </CardContent>
        </Card>
      </Grid>

      {/* Performance Metrics */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <SpeedIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Performance
            </Typography>
            <Box mb={1}>
              <Typography variant="body2" color="textSecondary">
                Scan Duration: {(scanResult.performance.scanDuration / 1000).toFixed(1)}s
              </Typography>
            </Box>
            <Box mb={1}>
              <Typography variant="body2" color="textSecondary">
                Memory Usage: {scanResult.performance.memoryUsage}MB
              </Typography>
            </Box>
            <Box mb={1}>
              <Typography variant="body2" color="textSecondary">
                Cache Hit Rate: {scanResult.performance.cacheHitRate}%
              </Typography>
            </Box>
            <LinearProgress
              variant="determinate"
              value={scanResult.performance.performanceScore}
              color="primary"
              sx={{ height: 6, borderRadius: 3 }}
            />
          </CardContent>
        </Card>
      </Grid>

      {/* VPS Health */}
      <Grid item xs={12} md={4}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <MemoryIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              VPS Health
            </Typography>
            <Box display="flex" alignItems="center" mb={2}>
              <Typography variant="h4" color={getScoreColor(scanResult.vpsPerformance.healthScore)}>
                {scanResult.vpsPerformance.performanceGrade}
              </Typography>
              <Typography variant="body2" color="textSecondary" ml={1}>
                ({scanResult.vpsPerformance.healthScore}%)
              </Typography>
            </Box>
            <Grid container spacing={1}>
              <Grid item xs={6}>
                <Typography variant="caption">
                  CPU: {scanResult.vpsPerformance.resourceUtilization.cpu}%
                </Typography>
              </Grid>
              <Grid item xs={6}>
                <Typography variant="caption">
                  Memory: {scanResult.vpsPerformance.resourceUtilization.memory}%
                </Typography>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Website Analysis Summary */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              <AIIcon sx={{ mr: 1, verticalAlign: 'middle' }} />
              Website Analysis
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={12} sm={6} md={3}>
                <Box textAlign="center">
                  <Typography variant="subtitle2" color="primary">
                    CMS Platform
                  </Typography>
                  <Typography variant="h6">{scanResult.websiteAnalysis.cms.platform}</Typography>
                  <Typography variant="caption" color="textSecondary">
                    {Math.round(scanResult.websiteAnalysis.cms.confidence * 100)}% confidence
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box textAlign="center">
                  <Typography variant="subtitle2" color="primary">
                    Framework
                  </Typography>
                  <Typography variant="h6">
                    {scanResult.websiteAnalysis.framework.primaryFramework}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    {Math.round(scanResult.websiteAnalysis.framework.confidence * 100)}% confidence
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box textAlign="center">
                  <Typography variant="subtitle2" color="primary">
                    E-commerce
                  </Typography>
                  <Typography variant="h6">
                    {scanResult.websiteAnalysis.ecommerce.platform || 'None'}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    Score: {scanResult.websiteAnalysis.ecommerce.overallScore}%
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Box textAlign="center">
                  <Typography variant="subtitle2" color="primary">
                    Media Elements
                  </Typography>
                  <Typography variant="h6">
                    {scanResult.websiteAnalysis.media.accessibleElements}/
                    {scanResult.websiteAnalysis.media.totalElements}
                  </Typography>
                  <Typography variant="caption" color="textSecondary">
                    {scanResult.websiteAnalysis.media.complianceLevel} Level
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>

      {/* Quick Stats */}
      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Test Results Summary
            </Typography>
            <Grid container spacing={2}>
              <Grid item xs={3}>
                <Box textAlign="center">
                  <CheckCircleIcon color="success" sx={{ fontSize: 40 }} />
                  <Typography variant="h4" color="success.main">
                    {scanResult.passedChecks}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Passed
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={3}>
                <Box textAlign="center">
                  <ErrorIcon color="error" sx={{ fontSize: 40 }} />
                  <Typography variant="h4" color="error.main">
                    {scanResult.failedChecks}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Failed
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={3}>
                <Box textAlign="center">
                  <WarningIcon color="warning" sx={{ fontSize: 40 }} />
                  <Typography variant="h4" color="warning.main">
                    {scanResult.warningChecks}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Warnings
                  </Typography>
                </Box>
              </Grid>
              <Grid item xs={3}>
                <Box textAlign="center">
                  <InfoIcon color="info" sx={{ fontSize: 40 }} />
                  <Typography variant="h4" color="info.main">
                    {scanResult.manualReviewItems.length}
                  </Typography>
                  <Typography variant="body2" color="textSecondary">
                    Manual Review
                  </Typography>
                </Box>
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderDetailedResultsTab = () => (
    <Box>
      <Typography variant="h6" gutterBottom>
        Detailed WCAG Rule Results
      </Typography>

      {scanResult.ruleResults.map((rule) => (
        <Accordion key={rule.ruleId} sx={{ mb: 1 }}>
          <AccordionSummary expandIcon={<ExpandMoreIcon />}>
            <Box display="flex" alignItems="center" width="100%">
              <Box mr={2}>
                {rule.status === 'pass' && <CheckCircleIcon color="success" />}
                {rule.status === 'fail' && <ErrorIcon color="error" />}
                {rule.status === 'warning' && <WarningIcon color="warning" />}
                {rule.status === 'manual' && <InfoIcon color="info" />}
              </Box>
              <Box flexGrow={1}>
                <Typography variant="subtitle1">{rule.ruleName}</Typography>
                <Typography variant="caption" color="textSecondary">
                  {rule.ruleId} • Level {rule.level} • {rule.impact} impact
                </Typography>
              </Box>
              <Chip
                label={rule.status}
                size="small"
                color={
                  rule.status === 'pass'
                    ? 'success'
                    : rule.status === 'fail'
                      ? 'error'
                      : rule.status === 'warning'
                        ? 'warning'
                        : 'info'
                }
              />
            </Box>
          </AccordionSummary>
          <AccordionDetails>
            <Typography variant="body2" paragraph>
              {rule.description}
            </Typography>

            {rule.elements.length > 0 && (
              <TableContainer component={Paper} variant="outlined">
                <Table size="small">
                  <TableHead>
                    <TableRow>
                      <TableCell>Element</TableCell>
                      <TableCell>Issue</TableCell>
                      <TableCell>Recommendation</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {rule.elements.map((element, idx) => (
                      <TableRow key={idx}>
                        <TableCell>
                          <code style={{ fontSize: '0.8em' }}>{element.selector}</code>
                        </TableCell>
                        <TableCell>{element.message}</TableCell>
                        <TableCell>{element.recommendation}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </AccordionDetails>
        </Accordion>
      ))}
    </Box>
  );

  const renderPerformanceTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Scan Performance Metrics
            </Typography>
            <Box mb={2}>
              <Typography variant="body2" gutterBottom>
                Scan Duration
              </Typography>
              <LinearProgress
                variant="determinate"
                value={Math.min(100, (30000 / scanResult.performance.scanDuration) * 100)}
                color="primary"
              />
              <Typography variant="caption" color="textSecondary">
                {(scanResult.performance.scanDuration / 1000).toFixed(1)}s (Target: &lt;30s)
              </Typography>
            </Box>

            <Box mb={2}>
              <Typography variant="body2" gutterBottom>
                Memory Efficiency
              </Typography>
              <LinearProgress
                variant="determinate"
                value={Math.max(0, 100 - scanResult.performance.memoryUsage / 20)}
                color="secondary"
              />
              <Typography variant="caption" color="textSecondary">
                {scanResult.performance.memoryUsage}MB used
              </Typography>
            </Box>

            <Box mb={2}>
              <Typography variant="body2" gutterBottom>
                Cache Hit Rate
              </Typography>
              <LinearProgress
                variant="determinate"
                value={scanResult.performance.cacheHitRate}
                color="success"
              />
              <Typography variant="caption" color="textSecondary">
                {scanResult.performance.cacheHitRate}% cache hits
              </Typography>
            </Box>
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              VPS Resource Utilization
            </Typography>
            {Object.entries(scanResult.vpsPerformance.resourceUtilization).map(
              ([resource, usage]) => (
                <Box key={resource} mb={2}>
                  <Typography variant="body2" gutterBottom sx={{ textTransform: 'capitalize' }}>
                    {resource}
                  </Typography>
                  <LinearProgress
                    variant="determinate"
                    value={usage}
                    color={usage > 80 ? 'error' : usage > 60 ? 'warning' : 'success'}
                  />
                  <Typography variant="caption" color="textSecondary">
                    {usage}% utilized
                  </Typography>
                </Box>
              ),
            )}
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Applied Optimizations
            </Typography>
            <Grid container spacing={1}>
              {scanResult.vpsPerformance.optimizationsApplied.map((optimization, index) => (
                <Grid item key={index}>
                  <Chip label={optimization} variant="outlined" color="primary" size="small" />
                </Grid>
              ))}
            </Grid>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  const renderRecommendationsTab = () => (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Priority Recommendations
            </Typography>
            {scanResult.recommendations.slice(0, 5).map((recommendation, index) => (
              <Alert key={index} severity="info" sx={{ mb: 1 }}>
                {recommendation}
              </Alert>
            ))}
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12} md={6}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Next Steps
            </Typography>
            {scanResult.nextSteps.map((step, index) => (
              <Alert key={index} severity="success" sx={{ mb: 1 }}>
                {step}
              </Alert>
            ))}
          </CardContent>
        </Card>
      </Grid>

      <Grid item xs={12}>
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom>
              Manual Review Items
            </Typography>
            <TableContainer>
              <Table>
                <TableHead>
                  <TableRow>
                    <TableCell>Rule</TableCell>
                    <TableCell>Description</TableCell>
                    <TableCell>Guidance</TableCell>
                    <TableCell>Priority</TableCell>
                  </TableRow>
                </TableHead>
                <TableBody>
                  {scanResult.manualReviewItems.map((item, index) => (
                    <TableRow key={index}>
                      <TableCell>{item.ruleId}</TableCell>
                      <TableCell>{item.description}</TableCell>
                      <TableCell>{item.guidance}</TableCell>
                      <TableCell>
                        <Chip
                          label={item.priority}
                          size="small"
                          color={
                            item.priority === 'high'
                              ? 'error'
                              : item.priority === 'medium'
                                ? 'warning'
                                : 'default'
                          }
                        />
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </TableContainer>
          </CardContent>
        </Card>
      </Grid>
    </Grid>
  );

  return (
    <Box>
      {/* Header */}
      <Box display="flex" justifyContent="space-between" alignItems="center" mb={3}>
        <Box>
          <Typography variant="h4" gutterBottom>
            WCAG Scan Results
          </Typography>
          <Typography variant="body1" color="textSecondary">
            {scanResult.url} • {new Date(scanResult.timestamp).toLocaleString()}
          </Typography>
        </Box>
        <Box>
          <Button variant="outlined" startIcon={<RefreshIcon />} onClick={onRescan} sx={{ mr: 1 }}>
            Rescan
          </Button>
          <Button variant="contained" onClick={onExportReport}>
            Export Report
          </Button>
        </Box>
      </Box>

      {/* Status Alert */}
      {scanResult.status === 'completed' && (
        <Alert severity="success" sx={{ mb: 3 }}>
          Scan completed successfully with enhanced analysis including CMS detection, framework
          optimization, and VPS performance monitoring.
        </Alert>
      )}

      {/* Main Content */}
      <Box sx={{ borderBottom: 1, borderColor: 'divider', mb: 3 }}>
        <Tabs value={activeTab} onChange={handleTabChange}>
          <Tab label="Overview" />
          <Tab label="Detailed Results" />
          <Tab label="Performance" />
          <Tab label="Recommendations" />
        </Tabs>
      </Box>

      {activeTab === 0 && renderOverviewTab()}
      {activeTab === 1 && renderDetailedResultsTab()}
      {activeTab === 2 && renderPerformanceTab()}
      {activeTab === 3 && renderRecommendationsTab()}
    </Box>
  );
};

export default WcagScanResults;
