/**
 * WCAG API Authentication Middleware
 * Keycloak protection with Bug-048 prevention
 */

import { Request, Response, NextFunction } from 'express';
import { z } from 'zod';
import rateLimit from 'express-rate-limit';
import helmet from 'helmet';
import logger from '../../../utils/logger';

// Extend Express Request type for user context
declare global {
  // eslint-disable-next-line @typescript-eslint/no-namespace
  namespace Express {
    interface Request {
      user?: {
        id: string;
        email: string;
        roles: string[];
        permissions: string[];
      };
      requestId?: string;
    }
  }
}

/**
 * Keycloak authentication middleware for WCAG endpoints
 * Prevents Bug-048 by ensuring proper authentication state
 */
export const authenticateWcagRequest = async (
  req: Request,
  res: Response,
  next: NextFunction,
): Promise<void> => {
  try {
    // Generate unique request ID for tracking
    req.requestId = require('uuid').v4();
    const requestId = req.requestId;

    logger.info(`🔐 [${requestId}] WCAG authentication check started`);
    logger.info(`📍 [${requestId}] Request: ${req.method} ${req.path}`);

    // Check for authorization header
    const authHeader = req.headers.authorization;
    logger.info(`🔑 [${requestId}] Authorization header: ${authHeader ? 'Present' : 'Missing'}`);

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      logger.warn(`❌ [${requestId}] Authentication failed: Missing or invalid authorization header`);
      res.status(401).json({
        success: false,
        error: {
          code: 'WCAG_AUTHENTICATION_ERROR',
          message: 'Missing or invalid authorization header',
          context: 'WCAG API access requires valid authentication token',
        },
        requestId,
        processingTime: 0,
      });
      return;
    }

    const token = authHeader.substring(7);
    logger.info(`🎫 [${requestId}] Token extracted, length: ${token.length}`);

    // Validate token with Keycloak (implementation depends on your Keycloak setup)
    logger.info(`🔍 [${requestId}] Validating token with Keycloak...`);
    const userContext = await validateKeycloakToken(token);

    if (!userContext) {
      logger.warn(`❌ [${requestId}] Authentication failed: Invalid or expired token`);
      res.status(401).json({
        success: false,
        error: {
          code: 'WCAG_AUTHENTICATION_ERROR',
          message: 'Invalid or expired authentication token',
          context: 'Please re-authenticate to access WCAG API',
        },
        requestId,
        processingTime: 0,
      });
      return;
    }

    // Check WCAG-specific permissions
    logger.info(`🔒 [${requestId}] Checking WCAG permissions for user: ${userContext.id}`);
    if (!hasWcagPermissions(userContext)) {
      logger.warn(`❌ [${requestId}] Authorization failed: Insufficient WCAG permissions`);
      res.status(403).json({
        success: false,
        error: {
          code: 'WCAG_AUTHORIZATION_ERROR',
          message: 'Insufficient permissions for WCAG compliance scanning',
          context: 'User lacks required WCAG scanning permissions',
        },
        requestId,
        processingTime: 0,
      });
      return;
    }

    // Attach user context to request (Bug-048 prevention)
    req.user = userContext;

    logger.info(`✅ [${requestId}] WCAG API access granted for user: ${userContext.id}`);
    logger.info(`👤 [${requestId}] User permissions: ${userContext.permissions.join(', ')}`);
    next();
  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    const currentRequestId = req.requestId || require('uuid').v4();

    logger.error(`❌ [${currentRequestId}] Authentication error:`, {
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined,
    });

    res.status(500).json({
      success: false,
      error: {
        code: 'WCAG_AUTHENTICATION_ERROR',
        message: 'Authentication service error',
        context: 'Unable to validate authentication token',
        details: process.env.NODE_ENV === 'development' ? { error: errorMessage } : undefined,
      },
      requestId: currentRequestId,
      processingTime: 0,
    });
  }
};

/**
 * Validate Keycloak token and extract user context
 * Following the same pattern as GDPR/HIPAA authentication
 */
async function validateKeycloakToken(token: string): Promise<{
  id: string;
  email: string;
  roles: string[];
  permissions: string[];
} | null> {
  try {
    const isDevelopment = process.env.NODE_ENV === 'development';

    if (isDevelopment) {
      // In development, use mock validation for easier testing
      logger.info('🧪 Development mode: Using mock token validation');
      logger.info(`🎫 Token preview: ${token.substring(0, 10)}...`);

      // Return mock user context for testing
      const mockUser = {
        id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b', // Use existing user UUID from database
        email: '<EMAIL>',
        roles: ['user', 'admin'],
        permissions: ['wcag:scan', 'wcag:view', 'wcag:export'],
      };

      logger.info(`👤 Mock user created: ${mockUser.id} (${mockUser.email})`);
      return mockUser;
    }

    // Production Keycloak validation
    logger.info('🔐 Production mode: Using Keycloak token validation');

    // Import the existing Keycloak instance
    const { keycloak } = await import('../../../lib/keycloak');

    // Validate the token using Keycloak's grant manager
    const grant = await keycloak.grantManager.createGrant({
      access_token: token as unknown as import('keycloak-connect').Token,
    });

    if (!grant || !grant.access_token) {
      logger.warn('🔐 Keycloak validation failed: Invalid grant or access token');
      return null;
    }

    // Extract user information from the token
    const tokenContent = (
      grant.access_token as unknown as {
        content: { sub: string; email?: string; realm_access?: { roles: string[] } };
      }
    ).content;

    if (!tokenContent || !tokenContent.sub) {
      logger.warn('🔐 Keycloak validation failed: Invalid token content');
      return null;
    }

    // Return user context with WCAG-specific permissions
    const userContext = {
      id: tokenContent.sub,
      email: tokenContent.email || `user-${tokenContent.sub}@comply-checker.local`,
      roles: tokenContent.realm_access?.roles || [],
      permissions: ['wcag:scan', 'wcag:view', 'wcag:export'], // Default WCAG permissions
    };

    logger.info(`🔐 Keycloak validation successful for user: ${userContext.id}`);
    return userContext;
  } catch (error) {
    logger.error('Keycloak token validation failed', { error });
    return null;
  }
}

/**
 * Check if user has required WCAG permissions
 */
function hasWcagPermissions(userContext: {
  id: string;
  email: string;
  roles: string[];
  permissions: string[];
}): boolean {
  const requiredPermissions = ['wcag:scan', 'wcag:view'];

  return requiredPermissions.every((permission) => userContext.permissions.includes(permission));
}

/**
 * Request validation middleware using Zod schemas
 */
export const validateRequest = <T>(schema: z.ZodSchema<T>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validationResult = schema.safeParse(req.body);

      if (!validationResult.success) {
        const errors = validationResult.error.errors.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));

        res.status(400).json({
          success: false,
          error: {
            code: 'WCAG_VALIDATION_ERROR',
            message: 'Request validation failed',
            details: { errors },
            context: 'Please check request format and required fields',
          },
          requestId: req.requestId || require('uuid').v4(),
          processingTime: 0,
        });
        return;
      }

      // Replace request body with validated data
      req.body = validationResult.data;
      next();
    } catch (error) {
      logger.error('Request validation error', { error });

      res.status(500).json({
        success: false,
        error: {
          code: 'WCAG_VALIDATION_ERROR',
          message: 'Request validation service error',
          context: 'Unable to validate request format',
        },
        requestId: req.requestId || require('uuid').v4(),
        processingTime: 0,
      });
    }
  };
};

/**
 * Query parameter validation middleware
 */
export const validateQuery = <T>(schema: z.ZodSchema<T>) => {
  return (req: Request, res: Response, next: NextFunction): void => {
    try {
      const validationResult = schema.safeParse(req.query);

      if (!validationResult.success) {
        const errors = validationResult.error.errors.map((err) => ({
          field: err.path.join('.'),
          message: err.message,
          code: err.code,
        }));

        res.status(400).json({
          success: false,
          error: {
            code: 'WCAG_VALIDATION_ERROR',
            message: 'Query parameter validation failed',
            details: { errors },
            context: 'Please check query parameters format',
          },
          requestId: req.requestId || require('uuid').v4(),
          processingTime: 0,
        });
        return;
      }

      // Replace query with validated data
      req.query = validationResult.data as Record<string, string | string[]>;
      next();
    } catch (error) {
      logger.error('Query validation error', { error });

      res.status(500).json({
        success: false,
        error: {
          code: 'WCAG_VALIDATION_ERROR',
          message: 'Query validation service error',
          context: 'Unable to validate query parameters',
        },
        requestId: req.requestId || require('uuid').v4(),
        processingTime: 0,
      });
    }
  };
};

/**
 * Rate limiting for WCAG API endpoints
 */
export const wcagRateLimit = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 50, // Limit each IP to 50 requests per windowMs
  message: {
    success: false,
    error: {
      code: 'WCAG_RATE_LIMIT_ERROR',
      message: 'Too many WCAG scan requests',
      context: 'Rate limit exceeded - please wait before making more requests',
    },
  },
  standardHeaders: true,
  legacyHeaders: false,
});

/**
 * Security headers for WCAG API
 */
export const wcagSecurityHeaders = helmet({
  contentSecurityPolicy: {
    directives: {
      defaultSrc: ["'self'"],
      scriptSrc: ["'self'"],
      styleSrc: ["'self'", "'unsafe-inline'"],
      imgSrc: ["'self'", 'data:', 'https:'],
      connectSrc: ["'self'"],
      fontSrc: ["'self'"],
      objectSrc: ["'none'"],
      mediaSrc: ["'self'"],
      frameSrc: ["'none'"],
    },
  },
  crossOriginEmbedderPolicy: false, // Allow for iframe scanning if needed
});

/**
 * Error handling middleware for WCAG API
 */
export const wcagErrorHandler = (
  error: Error,
  req: Request,
  res: Response,
  _next: NextFunction,
): void => {
  logger.error(`❌ [${req.requestId}] WCAG API Error`, { error });

  // Don't expose internal errors in production
  const isDevelopment = process.env.NODE_ENV === 'development';

  res.status(500).json({
    success: false,
    error: {
      code: 'WCAG_INTERNAL_ERROR',
      message: 'Internal server error during WCAG processing',
      details: isDevelopment ? { stack: error.stack } : undefined,
      context: 'An unexpected error occurred during WCAG compliance scanning',
    },
    requestId: req.requestId || require('uuid').v4(),
    processingTime: 0,
  });
};
