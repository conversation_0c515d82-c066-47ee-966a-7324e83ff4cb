/**
 * WCAG Database Service
 * Database operations for WCAG compliance data
 * Following HIPAA/GDPR patterns with real database operations using Knex ORM
 */

import db from '../../../lib/db';
import { v4 as uuidv4 } from 'uuid';
// import { Knex } from 'knex';
import { WcagScan<PERSON><PERSON>ult, WcagScanConfig, WcagRuleResult, ScanStatus, RiskLevel } from '../types';
import logger from '../../../utils/logger';

export interface ScanListOptions {
  page: number;
  limit: number;
  status?: ScanStatus;
  startDate?: Date;
  endDate?: Date;
  sortBy: 'scanTimestamp' | 'overallScore' | 'targetUrl';
  sortOrder: 'asc' | 'desc';
}

export interface ScanListResult {
  scans: Array<{
    scanId: string;
    targetUrl: string;
    status: ScanStatus;
    overallScore?: number;
    levelAchieved?: 'A' | 'AA' | 'AAA' | 'FAIL';
    riskLevel?: RiskLevel;
    scanTimestamp: string;
    completionTimestamp?: string;
    totalAutomatedChecks?: number;
    passedAutomatedChecks?: number;
    failedAutomatedChecks?: number;
    manualReviewItems?: number; // Count only, no scoring
  }>;
  pagination: {
    page: number;
    limit: number;
    total: number;
    totalPages: number;
  };
}

export interface WcagScanEntity {
  id: string;
  user_id: string;
  target_url: string;
  scan_timestamp: Date;
  scan_duration?: number;
  overall_score?: number;
  scan_status: ScanStatus;
  completion_timestamp?: Date;
  total_automated_checks: number;
  passed_automated_checks: number;
  failed_automated_checks: number;
  manual_review_items?: number;
  risk_level?: RiskLevel;
  level_achieved?: 'A' | 'AA' | 'AAA' | 'FAIL';
  perceivable_score?: number;
  operable_score?: number;
  understandable_score?: number;
  robust_score?: number;
  wcag21_score?: number;
  wcag22_score?: number;
  wcag30_score?: number;
  scan_options?: any;
  error_message?: string;
  created_at?: Date;
  updated_at?: Date;
}

export interface WcagAutomatedResultEntity {
  id: string;
  scan_id: string;
  rule_id: string;
  rule_name: string;
  category: string;
  wcag_version: string;
  conformance_level: string;
  automation_level: number;
  status: 'passed' | 'failed' | 'not_applicable';
  score: number;
  max_score: number;
  execution_time: number;
  details?: {
    totalChecks: number;
    passedChecks: number;
    evidence: Array<{
      type: string;
      description: string;
      value: string;
      selector?: string;
    }>;
    issues: string[];
    recommendations: string[];
    manualReviewItems: Array<{
      selector: string;
      description: string;
      automatedFindings: string;
      reviewRequired: string;
      priority: string;
    }>;
  };
  recommendations?: string[];
  created_at: Date;
}

export class WcagDatabase {
  /**
   * Safe JSON stringify helper to prevent database JSON errors
   * Following the proven GDPR pattern
   */
  private static stringifyJsonSafely(data: unknown): string {
    if (data === null || data === undefined) {
      return JSON.stringify([]);
    }
    try {
      // Ensure the data is properly serializable
      const serialized = JSON.stringify(data);
      // Validate it can be parsed back
      JSON.parse(serialized);
      return serialized;
    } catch (error) {
      logger.warn('Failed to stringify JSON safely:', { data, error });
      return JSON.stringify([]);
    }
  }

  /**
   * Clean data for JSONB columns without manual JSON escaping
   * Knex/PostgreSQL will handle the JSON serialization automatically
   */
  private static cleanDataForJsonb(data: unknown): unknown {
    if (data === null || data === undefined) {
      return data;
    }

    if (typeof data === 'string') {
      // Only remove control characters, don't escape quotes for JSONB
      return data
        .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
        .replace(/\n/g, ' ') // Replace newlines with spaces
        .replace(/\r/g, ' ') // Replace carriage returns with spaces
        .replace(/\t/g, ' ') // Replace tabs with spaces
        .replace(/\s+/g, ' ') // Normalize multiple spaces
        .trim();
    }

    if (Array.isArray(data)) {
      return data.map((item) => this.cleanDataForJsonb(item));
    }

    if (data && typeof data === 'object') {
      const cleaned: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(data)) {
        cleaned[key] = this.cleanDataForJsonb(value);
      }
      return cleaned;
    }

    return data;
  }

  /**
   * Safe JSONB data helper for PostgreSQL JSONB columns
   * Returns the actual data object/array for Knex to handle serialization
   */
  private static prepareJsonbData(data: unknown): unknown {
    if (data === null || data === undefined) {
      return [];
    }

    try {
      // If it's already an array or object, clean and return it
      if (Array.isArray(data)) {
        return data.map(item => this.cleanDataForJsonb(item));
      }

      if (typeof data === 'object') {
        return this.cleanDataForJsonb(data);
      }

      // If it's a string, try to parse it first
      if (typeof data === 'string') {
        try {
          const parsed = JSON.parse(data);
          return Array.isArray(parsed) ? parsed.map(item => this.cleanDataForJsonb(item)) : this.cleanDataForJsonb(parsed);
        } catch {
          // If parsing fails, treat as a single string item
          return [this.cleanDataForJsonb(data)];
        }
      }

      // For other types, wrap in array
      return [this.cleanDataForJsonb(data)];
    } catch (error) {
      logger.warn('Failed to prepare JSONB data safely:', { data, error });
      return [];
    }
  }

  /**
   * Clean a string to remove problematic characters that could break JSON
   */
  private static cleanStringForJson(str: string): string {
    if (typeof str !== 'string') {
      return String(str);
    }

    return str
      .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
      .replace(/\\/g, '\\\\') // Escape backslashes
      .replace(/"/g, '\\"') // Escape quotes
      .replace(/\n/g, ' ') // Replace newlines with spaces
      .replace(/\r/g, ' ') // Replace carriage returns with spaces
      .replace(/\t/g, ' ') // Replace tabs with spaces
      .replace(/\s+/g, ' ') // Normalize multiple spaces
      .trim();
  }

  /**
   * Clean data to remove problematic characters that could break JSON
   */
  private static cleanDataForJson(data: unknown): unknown {
    if (typeof data === 'string') {
      // Remove control characters and normalize the string
      return data
        .replace(/[\u0000-\u001F\u007F-\u009F]/g, '') // Remove control characters
        .replace(/\\/g, '\\\\') // Escape backslashes
        .replace(/"/g, '\\"') // Escape quotes
        .replace(/\n/g, '\\n') // Escape newlines
        .replace(/\r/g, '\\r') // Escape carriage returns
        .replace(/\t/g, '\\t') // Escape tabs
        .trim();
    }

    if (Array.isArray(data)) {
      return data.map((item) => WcagDatabase.cleanDataForJson(item));
    }

    if (data && typeof data === 'object') {
      const cleaned: Record<string, unknown> = {};
      for (const [key, value] of Object.entries(data)) {
        cleaned[key] = WcagDatabase.cleanDataForJson(value);
      }
      return cleaned;
    }

    return data;
  }

  /**
   * Create a new WCAG scan record
   */
  static async createScan(config: WcagScanConfig): Promise<string> {
    try {
      logger.info('📝 Creating WCAG scan record...');

      const insertData: Partial<WcagScanEntity> = {
        user_id: config.userId,
        target_url: config.targetUrl,
        scan_timestamp: new Date(),
        total_automated_checks: 0,
        passed_automated_checks: 0,
        failed_automated_checks: 0,
        manual_review_items: 0,
        scan_status: 'pending',
        scan_options: config.scanOptions || {},
      };

      // Use provided scanId if available, otherwise let database generate one
      if (config.requestId) {
        insertData.id = config.requestId;
      }

      const [scanId] = await db('wcag_scans').insert(insertData).returning('id');
      const finalScanId = typeof scanId === 'object' ? scanId.id : scanId;

      logger.info(`✅ WCAG scan record created with ID: ${finalScanId}`);
      return finalScanId;
    } catch (error) {
      logger.error('❌ Error creating WCAG scan record', { error });
      throw new Error(
        `Failed to create WCAG scan record: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get user's WCAG scans with pagination and filtering
   */
  async getUserScans(userId: string, options: ScanListOptions): Promise<ScanListResult> {
    try {
      logger.info(`📊 Fetching WCAG scans for user: ${userId}`, { options });

      let query = db('wcag_scans').where('user_id', userId).select('*');

      // Apply filters
      if (options.status) {
        query = query.where('scan_status', options.status);
      }

      if (options.startDate) {
        query = query.where('scan_timestamp', '>=', options.startDate);
      }

      if (options.endDate) {
        query = query.where('scan_timestamp', '<=', options.endDate);
      }

      // Get total count for pagination (before applying sorting and pagination)
      let countQuery = db('wcag_scans').where('user_id', userId);

      // Apply the same filters to count query
      if (options.status) {
        countQuery = countQuery.where('scan_status', options.status);
      }

      if (options.startDate) {
        countQuery = countQuery.where('scan_timestamp', '>=', options.startDate);
      }

      if (options.endDate) {
        countQuery = countQuery.where('scan_timestamp', '<=', options.endDate);
      }

      const [{ count }] = await countQuery.count('* as count');
      const total = parseInt(count as string, 10);

      // Apply sorting to the main query
      const sortColumn =
        options.sortBy === 'scanTimestamp'
          ? 'scan_timestamp'
          : options.sortBy === 'overallScore'
            ? 'overall_score'
            : 'target_url';
      query = query.orderBy(sortColumn, options.sortOrder);

      // Apply pagination
      const offset = (options.page - 1) * options.limit;
      const scans = await query.limit(options.limit).offset(offset);

      // Transform to expected format
      const transformedScans = scans.map((scan: WcagScanEntity) => ({
        scanId: scan.id,
        targetUrl: scan.target_url,
        status: scan.scan_status,
        overallScore: scan.overall_score ? Number(scan.overall_score) : undefined,
        levelAchieved: this.calculateLevelAchieved(scan.overall_score),
        riskLevel: scan.risk_level,
        scanTimestamp: scan.scan_timestamp.toISOString(),
        completionTimestamp: scan.completion_timestamp?.toISOString(),
        totalAutomatedChecks: scan.total_automated_checks,
        passedAutomatedChecks: scan.passed_automated_checks,
        failedAutomatedChecks: scan.failed_automated_checks,
        manualReviewItems: 0, // Will be calculated from manual_reviews table
      }));

      return {
        scans: transformedScans,
        pagination: {
          page: options.page,
          limit: options.limit,
          total,
          totalPages: Math.ceil(total / options.limit),
        },
      };
    } catch (error) {
      logger.error('❌ Error fetching WCAG scans', { error });
      throw new Error(
        `Failed to fetch WCAG scans: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Get scan by ID for specific user
   */
  async getScanById(scanId: string, userId: string): Promise<WcagScanResult | null> {
    try {
      logger.info(`🔍 Fetching WCAG scan: ${scanId} for user: ${userId}`);

      const scan = await db('wcag_scans').where({ id: scanId, user_id: userId }).first();

      if (!scan) {
        return null;
      }

      // Get automated results
      const automatedResults = await db('wcag_automated_results')
        .where('scan_id', scanId)
        .select('*');

      // Get manual review items
      const manualReviewItems = await this.getManualReviewItems(scanId);
      logger.info(`🔍 Debug - Manual review items fetched: ${manualReviewItems.length}`, { manualReviewItems });

      // Transform to WcagScanResult format
      const ruleResults: WcagRuleResult[] = automatedResults.map(
        (result: WcagAutomatedResultEntity) => ({
          ruleId: result.rule_id,
          ruleName: result.rule_name,
          category: result.category as 'perceivable' | 'operable' | 'understandable' | 'robust',
          wcagVersion: result.wcag_version as '2.1' | '2.2' | '3.0',
          successCriterion: '', // Not available in entity, will be populated from rule config
          level: result.conformance_level as 'A' | 'AA' | 'AAA',
          status: result.status,
          score: result.score,
          maxScore: result.max_score,
          weight: 1.0, // Default weight
          automated: true, // These are automated results
          evidence: [], // Not stored in entity, would need separate table
          recommendations: result.recommendations || [],
          executionTime: result.execution_time,
          errorMessage: undefined, // Not available in entity
          manualReviewItems: [], // Not stored in entity, would need separate table
        }),
      );

      // Normalize scores to 0-100 range
      const normalizeScore = (score: number | null | undefined): number => {
        if (!score) return 0;
        return Math.min(Math.max(Math.round(score), 0), 100);
      };

      // Build scan result
      const scanResult: WcagScanResult = {
        scanId: scan.id,
        targetUrl: scan.target_url,
        status: scan.scan_status,
        overallScore: normalizeScore(scan.overall_score),
        levelAchieved: scan.level_achieved || 'FAIL',
        riskLevel: scan.risk_level || 'medium',
        summary: {
          totalAutomatedChecks: scan.total_automated_checks || 0,
          passedAutomatedChecks: scan.passed_automated_checks || 0,
          failedAutomatedChecks: scan.failed_automated_checks || 0,
          automatedScore: normalizeScore(scan.overall_score),
          categoryScores: {
            perceivable: normalizeScore(scan.perceivable_score),
            operable: normalizeScore(scan.operable_score),
            understandable: normalizeScore(scan.understandable_score),
            robust: normalizeScore(scan.robust_score),
          },
          versionScores: {
            wcag21: normalizeScore(scan.wcag21_score),
            wcag22: normalizeScore(scan.wcag22_score),
            wcag30: normalizeScore(scan.wcag30_score),
          },
          automationRate: 0.87,
          manualReviewItems: manualReviewItems.length,
          // Additional properties for database compatibility
          overallScore: normalizeScore(scan.overall_score),
          totalRules: scan.total_automated_checks || 0,
          passedRules: scan.passed_automated_checks || 0,
          failedRules: scan.failed_automated_checks || 0,
          riskLevel: scan.risk_level || 'medium',
          scanDuration: scan.scan_duration || 0,
        },
        checks: ruleResults,
        recommendations: [],
        metadata: {
          scanId: scan.id,
          userId: scan.user_id,
          requestId: scan.id,
          startTime: scan.scan_timestamp,
          endTime: scan.completion_timestamp || undefined,
          duration: scan.scan_duration || 0,
          userAgent: 'Comply Checker Bot',
          viewport: { width: 1920, height: 1080 },
          environment: process.env.NODE_ENV || 'development',
          version: '1.0.0',
        },
        ruleResults,
      };

      // Add frontend compatibility properties using type assertion
      const extendedScanResult = scanResult as WcagScanResult & {
        url: string;
        scanTimestamp: string;
        completionTimestamp?: string;
        wcagVersion: string;
        complianceLevel: string;
        scanOptions: any;
        manualReviewData?: Array<{
          id: string;
          ruleId: string;
          selector: string;
          description: string;
          automatedFindings: string;
          reviewRequired: string;
          priority: 'high' | 'medium' | 'low';
          estimatedTime: number;
          reviewStatus: 'pending' | 'in_progress' | 'completed' | 'skipped';
          reviewerNotes?: string;
          reviewAssessment?: string;
          reviewedAt?: Date;
          reviewedBy?: string;
        }>;
      };

      extendedScanResult.url = scan.target_url;
      extendedScanResult.scanTimestamp = scan.scan_timestamp ? scan.scan_timestamp.toISOString() : new Date().toISOString();
      extendedScanResult.completionTimestamp = scan.completion_timestamp ? scan.completion_timestamp.toISOString() : undefined;
      extendedScanResult.wcagVersion = '2.1';
      extendedScanResult.complianceLevel = scan.level_achieved || 'FAIL';
      extendedScanResult.manualReviewData = manualReviewItems;
      extendedScanResult.scanOptions = scan.scan_options ? {
        enableContrastAnalysis: scan.scan_options.enableContrastAnalysis || true,
        enableKeyboardTesting: scan.scan_options.enableKeyboardTesting || true,
        enableFocusAnalysis: scan.scan_options.enableFocusAnalysis || true,
        enableSemanticValidation: scan.scan_options.enableSemanticValidation || true,
        wcagVersion: scan.scan_options.wcagVersion || '2.1',
        level: scan.scan_options.level || 'AA',
        maxPages: scan.scan_options.maxPages || 5,
        // Add aliases for frontend compatibility
        contrast: scan.scan_options.enableContrastAnalysis || true,
        keyboard: scan.scan_options.enableKeyboardTesting || true,
        focus: scan.scan_options.enableFocusAnalysis || true,
        semantic: scan.scan_options.enableSemanticValidation || true,
        pageLimit: scan.scan_options.maxPages || 5,
      } : {
        enableContrastAnalysis: true,
        enableKeyboardTesting: true,
        enableFocusAnalysis: true,
        enableSemanticValidation: true,
        wcagVersion: '2.1' as const,
        level: 'AA' as const,
        maxPages: 5,
        // Add aliases for frontend compatibility
        contrast: true,
        keyboard: true,
        focus: true,
        semantic: true,
        pageLimit: 5,
      };

      return extendedScanResult;
    } catch (error) {
      logger.error('❌ Error fetching WCAG scan', { error });
      throw new Error(
        `Failed to fetch WCAG scan: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Delete scan by ID for specific user
   */
  async deleteScan(scanId: string, userId: string): Promise<boolean> {
    try {
      logger.info(`🗑️ Deleting WCAG scan: ${scanId} for user: ${userId}`);

      const result = await db('wcag_scans').where({ id: scanId, user_id: userId }).del();

      return result > 0;
    } catch (error) {
      logger.error('❌ Error deleting WCAG scan', { error });
      throw new Error(
        `Failed to delete WCAG scan: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Health check for database connectivity
   */
  async healthCheck(): Promise<boolean> {
    try {
      logger.info('🏥 Checking WCAG database health');

      // Test basic database connectivity
      await db.raw('SELECT 1');

      // Check if WCAG tables exist
      const tables = ['wcag_scans', 'wcag_automated_results'];
      for (const table of tables) {
        const exists = await db.schema.hasTable(table);
        if (!exists) {
          logger.error(`❌ Table ${table} does not exist`);
          return false;
        }
      }

      logger.info('✅ WCAG database health check passed');
      return true;
    } catch (error) {
      logger.error('❌ WCAG database health check failed', { error });
      return false;
    }
  }

  /**
   * Save scan result to database
   */
  async saveScanResult(scanResult: WcagScanResult): Promise<void> {
    try {
      logger.info(`💾 Saving WCAG scan result: ${scanResult.scanId}`);

      // Update main scan record
      await db('wcag_scans').where('id', scanResult.scanId).update({
        overall_score: scanResult.summary.overallScore || scanResult.summary.automatedScore,
        scan_status: 'completed',
        completion_timestamp: new Date(),
        total_automated_checks: scanResult.summary.totalRules || scanResult.summary.totalAutomatedChecks,
        passed_automated_checks: scanResult.summary.passedRules || scanResult.summary.passedAutomatedChecks,
        failed_automated_checks: scanResult.summary.failedRules || scanResult.summary.failedAutomatedChecks,
        risk_level: scanResult.summary.riskLevel || scanResult.riskLevel,
        scan_duration: scanResult.summary.scanDuration || scanResult.metadata?.duration,
        level_achieved: scanResult.levelAchieved,
        perceivable_score: scanResult.summary.categoryScores?.perceivable,
        operable_score: scanResult.summary.categoryScores?.operable,
        understandable_score: scanResult.summary.categoryScores?.understandable,
        robust_score: scanResult.summary.categoryScores?.robust,
        wcag21_score: scanResult.summary.versionScores?.wcag21,
        wcag22_score: scanResult.summary.versionScores?.wcag22,
        wcag30_score: scanResult.summary.versionScores?.wcag30,
        manual_review_items: scanResult.summary.manualReviewItems,
      });

      // Save automated results
      if (scanResult.ruleResults && scanResult.ruleResults.length > 0) {
        const automatedResults = scanResult.ruleResults.map((result) => {
          // Ensure evidence and recommendations are properly serialized for JSONB
          let evidence: any[] = [];
          let recommendations: string[] = [];

          try {
            // Handle evidence - ensure it's a proper array of objects
            if (Array.isArray(result.evidence)) {
              evidence = result.evidence.map(item => {
                if (typeof item === 'string') {
                  return {
                    type: 'text',
                    description: WcagDatabase.cleanDataForJsonb(item) as string,
                    value: WcagDatabase.cleanDataForJsonb(item) as string
                  };
                }
                // Ensure all evidence objects have required fields and clean all string values
                return {
                  type: item.type || 'text',
                  description: WcagDatabase.cleanDataForJsonb(item.description || '') as string,
                  value: WcagDatabase.cleanDataForJsonb(item.value || '') as string,
                  selector: item.selector ? WcagDatabase.cleanDataForJsonb(item.selector) as string : undefined,
                  screenshot: item.screenshot ? WcagDatabase.cleanDataForJsonb(item.screenshot) as string : undefined,
                  severity: item.severity ? WcagDatabase.cleanDataForJsonb(item.severity) as string : undefined,
                  message: item.message ? WcagDatabase.cleanDataForJsonb(item.message) as string : undefined,
                  element: item.element ? WcagDatabase.cleanDataForJsonb(item.element) as string : undefined,
                  details: item.details ? WcagDatabase.cleanDataForJsonb(item.details) as string : undefined,
                };
              });
            } else if (result.evidence) {
              evidence = [{
                type: 'text',
                description: 'Evidence',
                value: WcagDatabase.cleanDataForJsonb(result.evidence) as string
              }];
            }

            // Handle recommendations - ensure it's a proper array of strings
            if (Array.isArray(result.recommendations)) {
              recommendations = result.recommendations.map((item) => WcagDatabase.cleanDataForJsonb(item) as string);
            } else if (result.recommendations) {
              recommendations = [WcagDatabase.cleanDataForJsonb(result.recommendations) as string];
            }
          } catch (serializationError: any) {
            logger.error(`Error serializing evidence/recommendations for rule ${result.ruleId}:`, {
              error: serializationError instanceof Error ? serializationError.message : 'Unknown error'
            });
            evidence = [{ type: 'error', description: 'Serialization error', value: 'Could not serialize evidence' }];
            recommendations = ['Manual review required due to data serialization error'];
          }

          return {
            id: uuidv4(),
            scan_id: scanResult.scanId,
            rule_id: result.ruleId,
            rule_name: result.ruleName,
            category: result.category,
            wcag_version: result.wcagVersion,
            success_criterion: result.successCriterion || '',
            level: result.level,
            status: result.status,
            score: result.score,
            max_score: result.maxScore,
            weight: result.weight || 1.0,
            evidence: JSON.stringify(WcagDatabase.prepareJsonbData(evidence)), // JSONB requires JSON string for Knex
            recommendations: JSON.stringify(WcagDatabase.prepareJsonbData(recommendations)), // JSONB requires JSON string for Knex
            execution_time: result.executionTime || 0,
            error_message: result.errorMessage || null,
            created_at: new Date(),
          };
        });

        await db('wcag_automated_results').insert(automatedResults);
      }

      logger.info(`✅ WCAG scan result saved: ${scanResult.scanId}`);
    } catch (error) {
      logger.error('❌ Error saving WCAG scan result', { error });
      throw new Error(
        `Failed to save WCAG scan result: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Update scan status
   */
  async updateScanStatus(scanId: string, status: ScanStatus, errorMessage?: string): Promise<void> {
    try {
      logger.info(`📝 Updating WCAG scan status: ${scanId} -> ${status}`);

      const updateData: Partial<WcagScanEntity> = {
        scan_status: status,
      };

      if (status === 'failed' && errorMessage) {
        (updateData as Record<string, unknown>).metadata = db.raw("COALESCE(metadata, '{}') || ?", [
          JSON.stringify({ errorMessage }),
        ]);
      }

      if (status === 'completed') {
        updateData.completion_timestamp = new Date();
      }

      await db('wcag_scans').where('id', scanId).update(updateData);

      logger.info(`✅ WCAG scan status updated: ${scanId} -> ${status}`);
    } catch (error) {
      logger.error('❌ Error updating WCAG scan status', { error });
      throw new Error(
        `Failed to update WCAG scan status: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Update manual review status for a specific WCAG check
   */
  async updateManualReview(
    scanId: string,
    ruleId: string,
    selector: string,
    reviewData: {
      assessment: string;
      notes: string;
      reviewerName: string;
      reviewedAt: string;
      reviewedBy: string;
    },
  ): Promise<boolean> {
    try {
      logger.info(`📝 Updating WCAG manual review for scan ${scanId}, rule ${ruleId}, selector ${selector}`, { reviewData });

      // Check if the manual review record exists
      const existingRecord = await db('wcag_manual_reviews')
        .where({ scan_id: scanId, rule_id: ruleId, element_selector: selector })
        .first();

      if (!existingRecord) {
        logger.warn(`⚠️ No WCAG manual review record found for scan ${scanId}, rule ${ruleId}, selector ${selector}`);

        // List all available manual review records for this scan for debugging
        const allRecords = await db('wcag_manual_reviews')
          .where({ scan_id: scanId })
          .select('rule_id', 'element_selector', 'description');

        logger.info(`🔍 Available manual review rules for scan ${scanId}:`, {
          records: allRecords,
          count: allRecords.length
        });
        return false;
      }

      // Update the manual review record
      // First check if the columns exist, if not add them
      try {
        await db.schema.alterTable('wcag_manual_reviews', (table) => {
          table.string('review_assessment').nullable();
          table.string('reviewed_by').nullable();
          table.timestamp('updated_at').nullable();
        });
      } catch (error) {
        // Columns might already exist, ignore error
        logger.info('Manual review columns may already exist, continuing...');
      }

      const updateData: any = {
        review_status: 'completed',
        reviewer_notes: reviewData.notes,
        reviewed_at: new Date(reviewData.reviewedAt),
      };

      // Only add these fields if the columns exist
      try {
        updateData.review_assessment = reviewData.assessment;
        updateData.reviewed_by = reviewData.reviewedBy;
        updateData.updated_at = new Date();
      } catch (error) {
        logger.warn('Some manual review columns not available, using basic update');
      }

      const updateResult = await db('wcag_manual_reviews')
        .where({ scan_id: scanId, rule_id: ruleId, element_selector: selector })
        .update(updateData);

      logger.info(`💾 WCAG manual review update result: ${updateResult} rows affected`);

      if (updateResult === 0) {
        logger.warn(`⚠️ No WCAG manual review records updated for scan ${scanId}, rule ${ruleId}, selector ${selector}`);
        return false;
      }

      logger.info(`✅ WCAG manual review updated successfully for scan ${scanId}, rule ${ruleId}, selector ${selector}`);
      return true;
    } catch (error) {
      logger.error('❌ Error updating WCAG manual review:', {
        error: error instanceof Error ? error.message : 'Unknown error',
        scanId,
        ruleId
      });
      throw error;
    }
  }

  /**
   * Get manual review items for a scan
   */
  async getManualReviewItems(scanId: string): Promise<Array<{
    id: string;
    ruleId: string;
    selector: string;
    description: string;
    automatedFindings: string;
    reviewRequired: string;
    priority: 'high' | 'medium' | 'low';
    estimatedTime: number;
    reviewStatus: 'pending' | 'in_progress' | 'completed' | 'skipped';
    reviewerNotes?: string;
    reviewAssessment?: string;
    reviewedAt?: Date;
    reviewedBy?: string;
  }>> {
    try {
      logger.info(`📋 Fetching manual review items for scan ${scanId}`);

      const manualReviews = await db('wcag_manual_reviews')
        .where('scan_id', scanId)
        .select('*')
        .orderBy('created_at', 'asc');

      return manualReviews.map((review) => ({
        id: review.id,
        ruleId: review.rule_id,
        selector: review.element_selector,
        description: review.description,
        automatedFindings: review.automated_findings,
        reviewRequired: review.review_required,
        priority: review.priority,
        estimatedTime: review.estimated_time,
        reviewStatus: review.review_status,
        reviewerNotes: review.reviewer_notes,
        reviewAssessment: review.review_assessment || null,
        reviewedAt: review.reviewed_at || null,
        reviewedBy: review.reviewed_by || null,
      }));
    } catch (error) {
      logger.error('❌ Error fetching manual review items', { error });
      throw new Error(
        `Failed to fetch manual review items: ${error instanceof Error ? error.message : 'Unknown error'}`,
      );
    }
  }

  /**
   * Store manual review items for a scan
   */
  async storeManualReviewItems(
    scanId: string,
    manualReviewItems: Array<{
      ruleId: string;
      selector: string;
      description: string;
      automatedFindings: string;
      reviewRequired: string;
      priority: 'high' | 'medium' | 'low';
      estimatedTime: number;
    }>,
  ): Promise<void> {
    try {
      if (manualReviewItems.length === 0) {
        logger.info(`📝 No manual review items to store for scan ${scanId}`);
        return;
      }

      logger.info(`📝 Storing ${manualReviewItems.length} manual review items for scan ${scanId}`);

      const manualReviewRecords = manualReviewItems.map((item) => ({
        id: uuidv4(),
        scan_id: scanId,
        rule_id: item.ruleId,
        element_selector: item.selector,
        description: item.description,
        automated_findings: item.automatedFindings,
        review_required: item.reviewRequired,
        priority: item.priority,
        estimated_time: item.estimatedTime,
        review_status: 'pending',
        created_at: new Date(),
      }));

      await db('wcag_manual_reviews').insert(manualReviewRecords);

      logger.info(`✅ Stored ${manualReviewItems.length} manual review items for scan ${scanId}`);
    } catch (error) {
      logger.error(`❌ Failed to store manual review items for scan ${scanId}:`, {
        error: error instanceof Error ? error.message : 'Unknown error',
        scanId,
        itemCount: manualReviewItems.length
      });
      throw error;
    }
  }

  /**
   * Get scan progress (placeholder for orchestrator compatibility)
   */
  async getScanProgress(scanId: string): Promise<{
    scanId: string;
    status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
    currentRule?: string;
    completedRules: string[];
    totalRules: number;
    progress: number;
    estimatedTimeRemaining?: number;
    startTime: Date;
    lastUpdate: Date;
  } | null> {
    // This would typically be stored in a separate progress table or cache
    // For now, return basic scan information
    try {
      // Get scan without user restriction for progress checking
      const scan = await db('wcag_scans').where('id', scanId).first();
      if (!scan) {
        return null;
      }

      return {
        scanId,
        status: scan.status as 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled',
        currentRule: undefined,
        completedRules: [],
        totalRules: 0,
        progress: scan.status === 'completed' ? 100 : 0,
        estimatedTimeRemaining: undefined,
        startTime: new Date(scan.created_at),
        lastUpdate: new Date(scan.updated_at),
      };
    } catch (error) {
      logger.error(`❌ Error getting scan progress`, { error });
      return null;
    }
  }

  /**
   * Store scan progress (placeholder for orchestrator compatibility)
   */
  async storeScanProgress(progress: {
    scanId: string;
    status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
    currentRule?: string;
    completedRules: string[];
    totalRules: number;
    progress: number;
    estimatedTimeRemaining?: number;
    startTime: Date;
    lastUpdate: Date;
  }): Promise<void> {
    // This would typically store progress in a separate table or cache
    // For now, just update the scan status
    try {
      if (progress.scanId && progress.status) {
        // Map progress status to ScanStatus
        const scanStatus: ScanStatus =
          progress.status === 'initializing' ? 'pending' : progress.status;
        await this.updateScanStatus(progress.scanId, scanStatus);
      }
    } catch (error) {
      logger.error(`❌ Error storing scan progress`, { error });
    }
  }

  /**
   * Update scan progress (placeholder for orchestrator compatibility)
   */
  async updateScanProgress(progress: {
    scanId: string;
    status: 'initializing' | 'running' | 'completed' | 'failed' | 'cancelled';
    currentRule?: string;
    completedRules: string[];
    totalRules: number;
    progress: number;
    estimatedTimeRemaining?: number;
    startTime: Date;
    lastUpdate: Date;
  }): Promise<void> {
    // This would typically update progress in a separate table or cache
    // For now, just update the scan status
    try {
      if (progress.scanId && progress.status) {
        // Map progress status to ScanStatus
        const scanStatus: ScanStatus =
          progress.status === 'initializing' ? 'pending' : progress.status;
        await this.updateScanStatus(progress.scanId, scanStatus);
      }
    } catch (error) {
      logger.error(`❌ Error updating scan progress`, { error });
    }
  }

  /**
   * Helper method to calculate level achieved based on score
   */
  private calculateLevelAchieved(score?: number): 'A' | 'AA' | 'AAA' | 'FAIL' {
    if (!score) return 'FAIL';
    if (score >= 95) return 'AAA';
    if (score >= 80) return 'AA';
    if (score >= 60) return 'A';
    return 'FAIL';
  }
}
