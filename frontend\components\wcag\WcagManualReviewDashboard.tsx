'use client';

import React, { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { WcagScanResult, WcagCheck, WcagCheckEvidence } from '@/types/wcag';
import wcagApiService from '@/services/wcag-api';
import {
  Clock,
  AlertTriangle,
  CheckCircle,
  FileText,
  Eye,
  Focus,
  MousePointer,
  Keyboard,
} from 'lucide-react';

interface WcagManualReviewItem {
  ruleId: string;
  ruleName: string;
  category: string;
  selector: string;
  description: string;
  automatedFindings: string;
  reviewRequired: string;
  priority: 'high' | 'medium' | 'low';
  estimatedTime: number;
  reviewStatus: 'pending' | 'in_progress' | 'completed' | 'skipped';
  reviewNotes?: string;
  reviewerAssessment?: 'compliant' | 'non_compliant' | 'needs_improvement';
  reviewDate?: string;
  reviewerId?: string;
}

interface WcagManualReviewSummary {
  totalItems: number;
  pendingReview: number;
  inProgress: number;
  completed: number;
  complianceRate: number;
}

interface WcagManualReviewDashboardProps {
  scanResult: WcagScanResult;
  onReviewUpdate?: (scanId: string, updatedScore: number) => void;
}

export function WcagManualReviewDashboard({
  scanResult,
  onReviewUpdate,
}: WcagManualReviewDashboardProps) {
  const [reviewNotes, setReviewNotes] = useState<Record<string, string>>({});
  const [reviewAssessments, setReviewAssessments] = useState<Record<string, string>>({});
  const [reviewStatuses, setReviewStatuses] = useState<
    Record<string, 'pending' | 'in_progress' | 'completed' | 'skipped'>
  >({});
  const [savingReviews, setSavingReviews] = useState<Record<string, boolean>>({});
  const [saveErrors, setSaveErrors] = useState<Record<string, string>>({});
  const [saveSuccess, setSaveSuccess] = useState<Record<string, boolean>>({});

  // Load existing manual review data when component mounts
  useEffect(() => {
    const loadExistingReviews = () => {
      // Check if scan result has manual review data
      if (scanResult.manualReviewData) {
        const existingAssessments: Record<string, string> = {};
        const existingNotes: Record<string, string> = {};
        const existingStatuses: Record<string, 'pending' | 'completed'> = {};

        scanResult.manualReviewData.forEach((review) => {
          const itemKey = `${review.ruleId}-${review.selector}`;

          if (review.reviewAssessment) {
            existingAssessments[itemKey] = review.reviewAssessment;
          }

          if (review.reviewerNotes) {
            existingNotes[itemKey] = review.reviewerNotes;
          }

          existingStatuses[itemKey] = review.reviewStatus === 'completed' ? 'completed' : 'pending';
        });

        setReviewAssessments(existingAssessments);
        setReviewNotes(existingNotes);
        setReviewStatuses(existingStatuses);
      }
    };

    loadExistingReviews();
  }, [scanResult]);

  // Extract manual review items from scan result
  const getManualReviewItems = (): WcagManualReviewItem[] => {
    const items: WcagManualReviewItem[] = [];

    // Debug: Log the scan result to see what data we have
    console.log('🔍 Debug - scanResult.manualReviewData:', scanResult.manualReviewData);
    console.log('🔍 Debug - scanResult.summary:', scanResult.summary);

    // First, try to get manual review items from the scan result's manualReviewData
    if (scanResult.manualReviewData && scanResult.manualReviewData.length > 0) {
      scanResult.manualReviewData.forEach((reviewData) => {
        items.push({
          ruleId: reviewData.ruleId,
          ruleName: reviewData.description, // Use description as rule name
          category: 'accessibility', // Default category
          selector: reviewData.selector,
          description: reviewData.description,
          automatedFindings: reviewData.automatedFindings,
          reviewRequired: reviewData.reviewRequired,
          priority: reviewData.priority,
          estimatedTime: reviewData.estimatedTime,
          reviewStatus: reviewData.reviewStatus,
          reviewNotes: reviewData.reviewerNotes,
          reviewerAssessment: reviewData.reviewAssessment as
            | 'compliant'
            | 'non_compliant'
            | 'needs_improvement',
          reviewDate: reviewData.reviewedAt?.toString(),
          reviewerId: reviewData.reviewedBy,
        });
      });
      return items;
    }

    // Fallback: Get manual review items from checks that have them
    scanResult.checks?.forEach((check) => {
      // Check if this is a semi-automated or manual check that needs review
      if (check.automationLevel === 'semi-automated' || check.automationLevel === 'manual') {
        // For semi-automated checks that failed or have warnings
        if (check.result === 'fail' || check.result === 'cantTell') {
          check.evidence?.forEach((evidence, index) => {
            items.push({
              ruleId: check.ruleId,
              ruleName: check.title,
              category: check.category,
              selector: evidence.selector || `element-${index}`,
              description: evidence.description,
              automatedFindings: `${check.description} - ${evidence.description}`,
              reviewRequired: `Manual review required for ${check.title} (${check.level} level)`,
              priority:
                check.impact === 'critical' || check.impact === 'serious'
                  ? 'high'
                  : check.impact === 'moderate'
                    ? 'medium'
                    : 'low',
              estimatedTime: 5, // Default 5 minutes
              reviewStatus: 'pending',
            });
          });
        }
      }
    });

    // Last resort: If no items found from checks and no manualReviewData, don't create placeholders
    // The placeholders were causing issues because they don't match the database records
    if (items.length === 0 && scanResult.summary?.manualReviewItems > 0) {
      console.log('⚠️ Manual review items expected but not found in manualReviewData or checks');
      console.log(
        '📊 Summary indicates',
        scanResult.summary.manualReviewItems,
        'manual review items should exist',
      );
      // Don't create placeholder items - they cause rule ID mismatches
    }

    return items;
  };

  const manualReviewItems = getManualReviewItems();

  // Calculate summary statistics
  const getManualReviewSummary = (): WcagManualReviewSummary => {
    const totalItems = manualReviewItems.length;
    let completed = 0;
    let pendingReview = 0;
    let inProgress = 0;

    manualReviewItems.forEach((item) => {
      const itemKey = `${item.ruleId}-${item.selector}`;
      const status = reviewStatuses[itemKey] || item.reviewStatus;

      if (status === 'completed') {
        completed++;
      } else if (status === 'in_progress') {
        inProgress++;
      } else {
        pendingReview++;
      }
    });

    const complianceRate = totalItems > 0 ? (completed / totalItems) * 100 : 0;

    return {
      totalItems,
      pendingReview,
      inProgress,
      completed,
      complianceRate,
    };
  };

  const summary = getManualReviewSummary();

  const handleReviewUpdate = (itemKey: string, field: 'notes' | 'assessment', value: string) => {
    if (field === 'notes') {
      setReviewNotes((prev) => ({ ...prev, [itemKey]: value }));
    } else {
      setReviewAssessments((prev) => ({ ...prev, [itemKey]: value }));
    }
  };

  const handleSaveReview = async (itemKey: string) => {
    const assessment = reviewAssessments[itemKey];
    const notes = reviewNotes[itemKey];

    if (!assessment) {
      setSaveErrors((prev) => ({
        ...prev,
        [itemKey]: 'Please select an assessment before saving',
      }));
      return;
    }

    // Clear previous errors and success states
    setSaveErrors((prev) => ({ ...prev, [itemKey]: '' }));
    setSaveSuccess((prev) => ({ ...prev, [itemKey]: false }));
    setSavingReviews((prev) => ({ ...prev, [itemKey]: true }));

    try {
      // Find the manual review item to get the correct ruleId
      const manualReviewItem = manualReviewItems.find(
        (item) => `${item.ruleId}-${item.selector}` === itemKey,
      );

      if (!manualReviewItem) {
        setSaveErrors((prev) => ({ ...prev, [itemKey]: 'Manual review item not found' }));
        return;
      }

      const ruleId = manualReviewItem.ruleId;

      console.log('💾 Saving WCAG manual review:', {
        scanId: scanResult.scanId,
        ruleId,
        assessment,
        notes,
        timestamp: new Date().toISOString(),
      });

      // Call WCAG API to save review
      const result = await wcagApiService.updateManualReview(
        scanResult.scanId,
        ruleId,
        manualReviewItem.selector,
        assessment,
        notes,
        'Accessibility Reviewer',
      );

      if (result.success) {
        setReviewStatuses((prev) => ({ ...prev, [itemKey]: 'completed' }));
        setSaveSuccess((prev) => ({ ...prev, [itemKey]: true }));
        console.log('✅ WCAG manual review saved successfully');

        if (onReviewUpdate && result.updatedScore) {
          onReviewUpdate(scanResult.scanId, result.updatedScore);
        }

        // Clear success message after 3 seconds
        setTimeout(() => {
          setSaveSuccess((prev) => ({ ...prev, [itemKey]: false }));
        }, 3000);
      } else {
        setSaveErrors((prev) => ({
          ...prev,
          [itemKey]: 'Failed to save review. Please try again.',
        }));
      }
    } catch (error) {
      console.error('❌ Failed to save WCAG manual review:', error);
      const errorMessage = error instanceof Error ? error.message : 'An unexpected error occurred';
      setSaveErrors((prev) => ({ ...prev, [itemKey]: errorMessage }));
    } finally {
      setSavingReviews((prev) => ({ ...prev, [itemKey]: false }));
    }
  };

  const getManualReviewIcon = (ruleId: string) => {
    if (ruleId.includes('focus')) return <Focus className="h-5 w-5 text-blue-500" />;
    if (ruleId.includes('keyboard')) return <Keyboard className="h-5 w-5 text-green-500" />;
    if (ruleId.includes('target')) return <MousePointer className="h-5 w-5 text-purple-500" />;
    return <Eye className="h-5 w-5 text-orange-500" />;
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'default';
      case 'low':
        return 'secondary';
      default:
        return 'default';
    }
  };

  if (manualReviewItems.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Manual Review Items</CardTitle>
          <CardDescription>
            Items requiring human accessibility expertise assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8">
            <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
            <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
              No Manual Review Required
            </h3>
            <p className="text-gray-600 dark:text-gray-400">
              All accessibility checks were completed automatically. No manual review items were
              identified.
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Summary Statistics */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Total Items</CardTitle>
            <FileText className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{summary.totalItems}</div>
            <p className="text-xs text-muted-foreground">Requiring manual review</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Pending Review</CardTitle>
            <Clock className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-orange-600">{summary.pendingReview}</div>
            <p className="text-xs text-muted-foreground">Awaiting assessment</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <CheckCircle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">{summary.completed}</div>
            <p className="text-xs text-muted-foreground">Reviews completed</p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completion Rate</CardTitle>
            <AlertTriangle className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{Math.round(summary.complianceRate)}%</div>
            <p className="text-xs text-muted-foreground">Review progress</p>
          </CardContent>
        </Card>
      </div>

      {/* Progress Overview */}
      <Card>
        <CardHeader>
          <CardTitle>Review Progress</CardTitle>
          <CardDescription>Overall progress of manual accessibility reviews</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Completion Rate</span>
              <span>{Math.round(summary.complianceRate)}%</span>
            </div>
            <Progress value={summary.complianceRate} className="w-full" />
            <p className="text-xs text-muted-foreground">
              {summary.completed} of {summary.totalItems} items reviewed
            </p>
          </div>
        </CardContent>
      </Card>

      {/* Manual Review Items */}
      <Card>
        <CardHeader>
          <CardTitle>Manual Review Items</CardTitle>
          <CardDescription>
            Items requiring human accessibility expertise assessment
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {manualReviewItems.length === 0 ? (
              <div className="text-center py-8">
                <AlertTriangle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 dark:text-white mb-2">
                  No Manual Review Items Found
                </h3>
                <p className="text-gray-600 dark:text-gray-400 mb-4">
                  This scan doesn't have any manual review items, or they haven't been loaded yet.
                </p>
                <p className="text-sm text-gray-500 dark:text-gray-500">
                  Manual review items are created when you enable "Manual Review" during the scan
                  process.
                </p>
              </div>
            ) : (
              manualReviewItems.map((item, index) => {
                const itemKey = `${item.ruleId}-${item.selector}`;
                const isCompleted = reviewStatuses[itemKey] === 'completed';
                const isSaving = savingReviews[itemKey];

                return (
                  <div
                    key={itemKey}
                    className={`border rounded-lg p-6 transition-all duration-200 ${
                      isCompleted
                        ? 'bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800'
                        : 'bg-white dark:bg-gray-800 border-gray-200 dark:border-gray-700'
                    }`}
                  >
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex items-center gap-3">
                        {getManualReviewIcon(item.ruleId)}
                        <div>
                          <h4 className="font-medium text-gray-900 dark:text-white">
                            {item.ruleName}
                          </h4>
                          <p className="text-sm text-gray-600 dark:text-gray-400">
                            {item.category} • {item.selector}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={getPriorityColor(item.priority)}>
                          {item.priority} priority
                        </Badge>
                        <Badge variant="outline">
                          <Clock className="h-3 w-3 mr-1" />
                          {item.estimatedTime}m
                        </Badge>
                        {isCompleted && (
                          <Badge variant="default" className="bg-green-500 text-white">
                            <CheckCircle className="h-3 w-3 mr-1" />
                            Completed
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="space-y-4">
                      <div>
                        <h5 className="font-medium text-sm text-gray-900 dark:text-white mb-2">
                          Description
                        </h5>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {item.description}
                        </p>
                      </div>

                      <div>
                        <h5 className="font-medium text-sm text-gray-900 dark:text-white mb-2">
                          Automated Findings
                        </h5>
                        <p className="text-sm text-gray-600 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 p-3 rounded">
                          {item.automatedFindings}
                        </p>
                      </div>

                      <div>
                        <h5 className="font-medium text-sm text-gray-900 dark:text-white mb-2">
                          Review Required
                        </h5>
                        <p className="text-sm text-gray-600 dark:text-gray-400">
                          {item.reviewRequired}
                        </p>
                      </div>

                      {!isCompleted && (
                        <div className="space-y-4 pt-4 border-t">
                          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                            <div>
                              <Label
                                htmlFor={`assessment-${itemKey}`}
                                className="text-sm font-medium"
                              >
                                Assessment *
                              </Label>
                              <Select
                                value={reviewAssessments[itemKey] || ''}
                                onValueChange={(value) =>
                                  handleReviewUpdate(itemKey, 'assessment', value)
                                }
                              >
                                <SelectTrigger>
                                  <SelectValue placeholder="Select assessment" />
                                </SelectTrigger>
                                <SelectContent>
                                  <SelectItem value="compliant">Compliant</SelectItem>
                                  <SelectItem value="non-compliant">Non-Compliant</SelectItem>
                                  <SelectItem value="partially-compliant">
                                    Partially Compliant
                                  </SelectItem>
                                  <SelectItem value="needs-review">Needs Review</SelectItem>
                                </SelectContent>
                              </Select>
                            </div>
                          </div>

                          <div>
                            <Label htmlFor={`notes-${itemKey}`} className="text-sm font-medium">
                              Review Notes
                            </Label>
                            <Textarea
                              id={`notes-${itemKey}`}
                              placeholder="Add your review notes and recommendations..."
                              value={reviewNotes[itemKey] || ''}
                              onChange={(e) => handleReviewUpdate(itemKey, 'notes', e.target.value)}
                              className="mt-1"
                              rows={3}
                            />
                          </div>

                          {/* Error Message */}
                          {saveErrors[itemKey] && (
                            <Alert variant="destructive" className="mb-4">
                              <AlertTriangle className="h-4 w-4" />
                              <AlertDescription>{saveErrors[itemKey]}</AlertDescription>
                            </Alert>
                          )}

                          {/* Success Message */}
                          {saveSuccess[itemKey] && (
                            <Alert className="mb-4 border-green-200 bg-green-50 text-green-800">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              <AlertDescription>Review saved successfully!</AlertDescription>
                            </Alert>
                          )}

                          <div className="flex justify-end">
                            <Button
                              onClick={() => handleSaveReview(itemKey)}
                              disabled={!reviewAssessments[itemKey] || isSaving}
                              className="flex items-center gap-2"
                            >
                              {isSaving ? (
                                <>
                                  <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                                  Saving...
                                </>
                              ) : (
                                <>
                                  <CheckCircle className="h-4 w-4" />
                                  Save Review
                                </>
                              )}
                            </Button>
                          </div>
                        </div>
                      )}

                      {isCompleted && (
                        <div className="pt-4 border-t border-green-200">
                          <Alert className="border-green-200 bg-green-50 text-green-800">
                            <CheckCircle className="h-4 w-4 text-green-600" />
                            <AlertDescription>
                              <div className="space-y-2">
                                <div>
                                  <strong>Review Status:</strong> Completed
                                </div>
                                <div>
                                  <strong>Assessment:</strong>{' '}
                                  <span className="capitalize">
                                    {reviewAssessments[itemKey]
                                      ?.replace(/-/g, ' ')
                                      .replace(/_/g, ' ')}
                                  </span>
                                </div>
                                {reviewNotes[itemKey] && (
                                  <div>
                                    <strong>Review Notes:</strong>
                                    <div className="mt-1 p-2 bg-white rounded border text-gray-700 text-sm">
                                      {reviewNotes[itemKey]}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </AlertDescription>
                          </Alert>
                        </div>
                      )}
                    </div>
                  </div>
                );
              })
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  );
}

// Default export for dynamic imports
export default WcagManualReviewDashboard;
