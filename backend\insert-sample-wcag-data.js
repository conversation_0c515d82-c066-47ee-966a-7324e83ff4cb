/**
 * Insert sample WCAG data directly using Node.js
 */

const { Client } = require('pg');

async function insertSampleData() {
  const client = new Client({
    host: 'localhost',
    port: 5432,
    database: 'complychecker_dev',
    user: 'complyuser',
    password: 'complypassword',
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Check if sample data already exists
    const existingData = await client.query(
      "SELECT COUNT(*) as count FROM wcag_scans WHERE user_id = '9fed30a7-64b4-4ffe-b531-6e9cf592952b'"
    );

    if (parseInt(existingData.rows[0].count) > 0) {
      console.log('📊 Sample WCAG data already exists, skipping insertion');
      return;
    }

    console.log('🔄 Inserting sample WCAG data...');

    // Insert sample scans
    const sampleScans = [
      {
        id: 'sample-wcag-001',
        user_id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
        target_url: 'https://example.com',
        scan_status: 'completed',
        scan_timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000), // 1 day ago
        completion_timestamp: new Date(Date.now() - 24 * 60 * 60 * 1000 + 5 * 60 * 1000),
        overall_score: 85.5,
        level_achieved: 'AA',
        risk_level: 'low',
        perceivable_score: 90,
        operable_score: 85,
        understandable_score: 80,
        robust_score: 85,
        wcag21_score: 85,
        wcag22_score: 85,
        wcag30_score: 85,
        total_automated_checks: 25,
        passed_automated_checks: 20,
        failed_automated_checks: 5,
        manual_review_items: 3,
        scan_options: JSON.stringify({
          maxPages: 5,
          enableContrastAnalysis: true,
          enableKeyboardTesting: true,
          enableFocusAnalysis: true,
          enableSemanticValidation: true,
          wcagVersion: 'all',
          level: 'AA'
        })
      },
      {
        id: 'sample-wcag-002',
        user_id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
        target_url: 'https://test.example.com',
        scan_status: 'completed',
        scan_timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000), // 2 days ago
        completion_timestamp: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000 + 7 * 60 * 1000),
        overall_score: 72.3,
        level_achieved: 'A',
        risk_level: 'medium',
        perceivable_score: 75,
        operable_score: 70,
        understandable_score: 68,
        robust_score: 76,
        wcag21_score: 72,
        wcag22_score: 73,
        wcag30_score: 71,
        total_automated_checks: 25,
        passed_automated_checks: 18,
        failed_automated_checks: 7,
        manual_review_items: 5,
        scan_options: JSON.stringify({
          maxPages: 3,
          enableContrastAnalysis: true,
          enableKeyboardTesting: true,
          enableFocusAnalysis: false,
          enableSemanticValidation: true,
          wcagVersion: '2.1',
          level: 'AA'
        })
      },
      {
        id: 'sample-wcag-003',
        user_id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
        target_url: 'https://demo.example.com',
        scan_status: 'completed',
        scan_timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000), // 3 days ago
        completion_timestamp: new Date(Date.now() - 3 * 24 * 60 * 60 * 1000 + 4 * 60 * 1000),
        overall_score: 92.1,
        level_achieved: 'AAA',
        risk_level: 'low',
        perceivable_score: 95,
        operable_score: 90,
        understandable_score: 88,
        robust_score: 95,
        wcag21_score: 92,
        wcag22_score: 93,
        wcag30_score: 91,
        total_automated_checks: 30,
        passed_automated_checks: 28,
        failed_automated_checks: 2,
        manual_review_items: 1,
        scan_options: JSON.stringify({
          maxPages: 10,
          enableContrastAnalysis: true,
          enableKeyboardTesting: true,
          enableFocusAnalysis: true,
          enableSemanticValidation: true,
          wcagVersion: 'all',
          level: 'AAA'
        })
      },
      {
        id: 'sample-wcag-004',
        user_id: '9fed30a7-64b4-4ffe-b531-6e9cf592952b',
        target_url: 'https://blog.example.com',
        scan_status: 'running',
        scan_timestamp: new Date(Date.now() - 60 * 60 * 1000), // 1 hour ago
        completion_timestamp: null,
        overall_score: null,
        level_achieved: null,
        risk_level: null,
        perceivable_score: null,
        operable_score: null,
        understandable_score: null,
        robust_score: null,
        wcag21_score: null,
        wcag22_score: null,
        wcag30_score: null,
        total_automated_checks: 20,
        passed_automated_checks: 12,
        failed_automated_checks: 3,
        manual_review_items: 0,
        scan_options: JSON.stringify({
          maxPages: 5,
          enableContrastAnalysis: true,
          enableKeyboardTesting: true,
          enableFocusAnalysis: true,
          enableSemanticValidation: true,
          wcagVersion: '2.2',
          level: 'AA'
        })
      }
    ];

    for (const scan of sampleScans) {
      await client.query(`
        INSERT INTO wcag_scans (
          id, user_id, target_url, scan_status, scan_timestamp, completion_timestamp,
          overall_score, level_achieved, risk_level, perceivable_score, operable_score,
          understandable_score, robust_score, wcag21_score, wcag22_score, wcag30_score,
          total_automated_checks, passed_automated_checks, failed_automated_checks,
          manual_review_items, scan_options
        ) VALUES (
          $1, $2, $3, $4, $5, $6, $7, $8, $9, $10, $11, $12, $13, $14, $15, $16, $17, $18, $19, $20, $21
        )
      `, [
        scan.id, scan.user_id, scan.target_url, scan.scan_status, scan.scan_timestamp,
        scan.completion_timestamp, scan.overall_score, scan.level_achieved, scan.risk_level,
        scan.perceivable_score, scan.operable_score, scan.understandable_score, scan.robust_score,
        scan.wcag21_score, scan.wcag22_score, scan.wcag30_score, scan.total_automated_checks,
        scan.passed_automated_checks, scan.failed_automated_checks, scan.manual_review_items,
        scan.scan_options
      ]);
    }

    console.log('✅ Sample WCAG scans inserted successfully');

    // Insert some sample automated results
    await client.query(`
      INSERT INTO wcag_automated_results (
        id, scan_id, rule_id, rule_name, category, wcag_version, success_criterion,
        level, status, score, max_score, weight, evidence, recommendations, execution_time
      ) VALUES 
      (
        'result-001', 'sample-wcag-001', 'WCAG-001', 'Image Alternative Text',
        'perceivable', '2.1', '1.1.1', 'A', 'passed', 100, 100, 1.0,
        '[{"type": "text", "description": "All images have appropriate alt text", "value": "15 images checked", "severity": "info"}]',
        '["Continue providing descriptive alt text for all images"]', 150
      ),
      (
        'result-002', 'sample-wcag-001', 'WCAG-007', 'Focus Visible',
        'operable', '2.1', '2.4.7', 'AA', 'failed', 70, 100, 1.0,
        '[{"type": "text", "description": "Some elements lack visible focus indicators", "value": "3 elements failed", "severity": "error"}]',
        '["Add visible focus indicators to all interactive elements", "Ensure focus indicators have sufficient contrast"]', 200
      )
    `);

    console.log('✅ Sample WCAG automated results inserted successfully');
    console.log('🎉 All sample data inserted successfully!');

  } catch (error) {
    console.error('❌ Error inserting sample data:', error);
  } finally {
    await client.end();
  }
}

insertSampleData();
