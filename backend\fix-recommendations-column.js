/**
 * Fix Recommendations Column
 * Convert the recommendations column from text array to JSONB
 */

console.log('🔧 Fixing recommendations column type...');

// Load environment variables
const fs = require('fs');
const path = require('path');

const envPath = path.join(__dirname, '..', '.env');
if (fs.existsSync(envPath)) {
  const envContent = fs.readFileSync(envPath, 'utf8');
  envContent.split('\n').forEach(line => {
    const [key, value] = line.split('=');
    if (key && value && !key.startsWith('#')) {
      process.env[key.trim()] = value.trim();
    }
  });
}

process.env.NODE_ENV = 'development';

async function fixRecommendationsColumn() {
  try {
    // Set up TypeScript compilation
    require('ts-node/register');
    require('tsconfig-paths/register');
    
    console.log('✅ TypeScript setup complete');
    
    // Import database connection
    const db = require('./src/lib/db').default;
    console.log('✅ Database connection imported');
    
    // Check current column type
    const columns = await db.raw(`
      SELECT column_name, data_type, udt_name 
      FROM information_schema.columns 
      WHERE table_name = 'wcag_automated_results' 
      AND column_name = 'recommendations'
    `);
    
    const recommendationsCol = columns.rows[0];
    console.log('📊 Current recommendations column:', recommendationsCol);
    
    if (recommendationsCol.udt_name === '_text') {
      console.log('🔧 Converting recommendations column from text array to JSONB...');
      
      // First, let's see if there's any existing data
      const existingData = await db('wcag_automated_results').select('id', 'recommendations').limit(5);
      console.log('📋 Existing data sample:', existingData);
      
      try {
        // Convert the column type
        await db.raw('ALTER TABLE wcag_automated_results ALTER COLUMN recommendations TYPE jsonb USING recommendations::jsonb');
        console.log('✅ Recommendations column converted to JSONB successfully!');
      } catch (alterError) {
        console.log('⚠️ Direct conversion failed, trying alternative approach...');
        console.log('Error:', alterError.message);
        
        // Alternative approach: drop and recreate the column
        console.log('🔧 Dropping and recreating recommendations column...');
        
        // First backup any existing data
        const backupData = await db('wcag_automated_results').select('*');
        console.log(`📋 Backing up ${backupData.length} existing records...`);
        
        // Drop the column
        await db.raw('ALTER TABLE wcag_automated_results DROP COLUMN recommendations');
        console.log('✅ Old recommendations column dropped');
        
        // Add new JSONB column
        await db.raw('ALTER TABLE wcag_automated_results ADD COLUMN recommendations jsonb');
        console.log('✅ New JSONB recommendations column added');
        
        // Restore data if any exists
        if (backupData.length > 0) {
          console.log('🔧 Restoring data with proper JSONB format...');
          
          for (const record of backupData) {
            try {
              // Convert recommendations to proper JSONB format
              let recommendations = [];
              if (record.recommendations) {
                if (Array.isArray(record.recommendations)) {
                  recommendations = record.recommendations;
                } else if (typeof record.recommendations === 'string') {
                  try {
                    recommendations = JSON.parse(record.recommendations);
                  } catch {
                    recommendations = [record.recommendations];
                  }
                }
              }
              
              await db('wcag_automated_results')
                .where('id', record.id)
                .update({ recommendations });
                
            } catch (updateError) {
              console.log(`⚠️ Failed to update record ${record.id}:`, updateError.message);
            }
          }
          
          console.log('✅ Data restoration completed');
        }
      }
      
      // Verify the fix
      const updatedColumns = await db.raw(`
        SELECT column_name, data_type, udt_name 
        FROM information_schema.columns 
        WHERE table_name = 'wcag_automated_results' 
        AND column_name = 'recommendations'
      `);
      
      const updatedCol = updatedColumns.rows[0];
      console.log('📊 Updated recommendations column:', updatedCol);
      
      if (updatedCol.udt_name === 'jsonb') {
        console.log('🎉 Recommendations column is now JSONB!');
      } else {
        console.log('❌ Column type conversion failed');
      }
      
    } else if (recommendationsCol.udt_name === 'jsonb') {
      console.log('✅ Recommendations column is already JSONB');
    } else {
      console.log('⚠️ Unexpected column type:', recommendationsCol.udt_name);
    }
    
    console.log('🎉 Recommendations column fix completed!');
    
  } catch (error) {
    console.error('❌ Recommendations column fix failed:', error);
    console.error('Error details:', {
      message: error.message,
      code: error.code
    });
    
    if (error.message.includes('connect') || error.message.includes('database')) {
      console.log('💡 Database connection issue. Make sure PostgreSQL is running.');
    }
    
    process.exit(1);
  }
}

// Run the fix
console.log('🔧 Starting recommendations column fix...');
fixRecommendationsColumn()
  .then(() => {
    console.log('🎉 Recommendations column fix completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Recommendations column fix failed:', error);
    process.exit(1);
  });
