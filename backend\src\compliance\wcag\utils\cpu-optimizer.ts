/**
 * CPU Optimizer for VPS Environments
 * CPU-aware processing with workload distribution and priority-based scheduling
 */

import * as os from 'os';
import cluster from 'cluster';
import logger from '../../../utils/logger';

export interface CPUStats {
  cores: number;
  usage: number; // 0-100
  loadAverage: number[];
  processes: number;
  threads: number;
  utilization: {
    user: number;
    system: number;
    idle: number;
    nice: number;
    irq: number;
  };
}

export interface WorkloadTask {
  id: string;
  type: 'wcag-scan' | 'analysis' | 'cache-operation' | 'cleanup';
  priority: 'low' | 'normal' | 'high' | 'critical';
  estimatedCPUTime: number; // ms
  estimatedMemory: number; // MB
  dependencies: string[];
  callback: () => Promise<any>;
  timeout: number;
  retries: number;
  maxRetries: number;
  createdAt: number;
  startedAt?: number;
  completedAt?: number;
  error?: Error;
}

export interface WorkerStats {
  id: number;
  pid: number;
  status: 'idle' | 'busy' | 'error' | 'dead';
  currentTask?: string;
  tasksCompleted: number;
  totalCPUTime: number;
  averageTaskTime: number;
  lastActivity: number;
  memoryUsage: number;
}

export interface CPUOptimizationConfig {
  enableWorkerProcesses: boolean;
  maxWorkers: number;
  enableTaskQueue: boolean;
  enablePriorityScheduling: boolean;
  enableLoadBalancing: boolean;
  taskTimeout: number;
  maxRetries: number;
  cpuThreshold: number; // 0-100
  loadAverageThreshold: number;
}

/**
 * Advanced CPU optimizer with workload distribution and priority scheduling
 */
export class CPUOptimizer {
  private static instance: CPUOptimizer;
  private taskQueue: WorkloadTask[] = [];
  private runningTasks: Map<string, WorkloadTask> = new Map();
  private workers: Map<number, WorkerStats> = new Map();
  private cpuHistory: CPUStats[] = [];
  private config: CPUOptimizationConfig;
  private monitoringInterval: NodeJS.Timeout | null = null;
  private schedulerInterval: NodeJS.Timeout | null = null;
  private lastCPUMeasurement: any = null;

  private constructor() {
    this.config = this.getDefaultConfig();
    this.setupWorkerProcesses();
    this.startCPUMonitoring();
    this.startTaskScheduler();
  }

  static getInstance(): CPUOptimizer {
    if (!CPUOptimizer.instance) {
      CPUOptimizer.instance = new CPUOptimizer();
    }
    return CPUOptimizer.instance;
  }

  /**
   * Get current CPU statistics
   */
  async getCurrentCPUStats(): Promise<CPUStats> {
    const cpus = os.cpus();
    const loadAvg = os.loadavg();
    
    // Calculate CPU usage
    const cpuUsage = await this.calculateCPUUsage();
    
    const stats: CPUStats = {
      cores: cpus.length,
      usage: cpuUsage.total,
      loadAverage: loadAvg,
      processes: this.runningTasks.size,
      threads: this.workers.size,
      utilization: cpuUsage.detailed,
    };

    // Store in history (keep last 60 entries = 5 minutes at 5-second intervals)
    this.cpuHistory.push(stats);
    if (this.cpuHistory.length > 60) {
      this.cpuHistory.shift();
    }

    return stats;
  }

  /**
   * Schedule a task for execution
   */
  async scheduleTask(task: Omit<WorkloadTask, 'id' | 'createdAt' | 'retries'>): Promise<string> {
    const taskId = `task_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    
    const fullTask: WorkloadTask = {
      ...task,
      id: taskId,
      createdAt: Date.now(),
      retries: 0,
    };

    // Add to queue based on priority
    this.insertTaskByPriority(fullTask);

    logger.debug(`Task scheduled: ${taskId}`, {
      type: task.type,
      priority: task.priority,
      queueLength: this.taskQueue.length,
    });

    return taskId;
  }

  /**
   * Execute a task immediately (high priority)
   */
  async executeTaskImmediate(task: Omit<WorkloadTask, 'id' | 'createdAt' | 'retries'>): Promise<any> {
    const taskId = await this.scheduleTask({
      ...task,
      priority: 'critical',
    });

    // Wait for task completion
    return new Promise((resolve, reject) => {
      const checkCompletion = () => {
        const runningTask = this.runningTasks.get(taskId);
        if (runningTask) {
          if (runningTask.completedAt) {
            resolve(runningTask);
          } else if (runningTask.error) {
            reject(runningTask.error);
          } else {
            setTimeout(checkCompletion, 100);
          }
        } else {
          // Check if task is still in queue
          const queuedTask = this.taskQueue.find(t => t.id === taskId);
          if (queuedTask) {
            setTimeout(checkCompletion, 100);
          } else {
            reject(new Error('Task not found'));
          }
        }
      };
      
      checkCompletion();
    });
  }

  /**
   * Get optimal number of concurrent tasks based on CPU capacity
   */
  getOptimalConcurrency(): number {
    const cpuStats = this.cpuHistory.length > 0 
      ? this.cpuHistory[this.cpuHistory.length - 1]
      : null;

    if (!cpuStats) {
      return Math.max(1, Math.floor(os.cpus().length / 2));
    }

    // Base concurrency on CPU cores and current usage
    const baseConcurrency = os.cpus().length;
    const usageMultiplier = Math.max(0.2, (100 - cpuStats.usage) / 100);
    const loadMultiplier = Math.max(0.2, Math.min(1, 2 - cpuStats.loadAverage[0]));
    
    const optimalConcurrency = Math.floor(baseConcurrency * usageMultiplier * loadMultiplier);
    
    return Math.max(1, Math.min(optimalConcurrency, this.config.maxWorkers));
  }

  /**
   * Distribute workload across available resources
   */
  async distributeWorkload(tasks: WorkloadTask[]): Promise<void> {
    const optimalConcurrency = this.getOptimalConcurrency();
    const availableSlots = optimalConcurrency - this.runningTasks.size;

    if (availableSlots <= 0) {
      logger.debug('No available slots for workload distribution');
      return;
    }

    // Sort tasks by priority and estimated CPU time
    const sortedTasks = tasks
      .sort((a, b) => {
        const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 };
        const priorityDiff = priorityOrder[b.priority] - priorityOrder[a.priority];
        
        if (priorityDiff !== 0) return priorityDiff;
        
        // If same priority, prefer shorter tasks
        return a.estimatedCPUTime - b.estimatedCPUTime;
      })
      .slice(0, availableSlots);

    // Execute tasks
    for (const task of sortedTasks) {
      await this.executeTask(task);
    }
  }

  /**
   * Get CPU optimization recommendations
   */
  getCPUOptimizationRecommendations(): string[] {
    const recommendations: string[] = [];
    const currentStats = this.cpuHistory.length > 0 
      ? this.cpuHistory[this.cpuHistory.length - 1]
      : null;

    if (!currentStats) {
      return ['Insufficient CPU data for recommendations'];
    }

    // High CPU usage
    if (currentStats.usage > 90) {
      recommendations.push('Critical CPU usage - consider reducing concurrent operations');
    } else if (currentStats.usage > 80) {
      recommendations.push('High CPU usage - monitor performance and consider optimization');
    }

    // High load average
    if (currentStats.loadAverage[0] > os.cpus().length * 2) {
      recommendations.push('High system load - consider reducing workload or scaling resources');
    }

    // Queue backlog
    if (this.taskQueue.length > 10) {
      recommendations.push(`Task queue backlog (${this.taskQueue.length} tasks) - consider increasing concurrency`);
    }

    // Worker efficiency
    const idleWorkers = Array.from(this.workers.values()).filter(w => w.status === 'idle').length;
    const totalWorkers = this.workers.size;
    
    if (totalWorkers > 0 && idleWorkers / totalWorkers > 0.5) {
      recommendations.push('Many idle workers - consider reducing worker count or increasing workload');
    }

    // Long-running tasks
    const longRunningTasks = Array.from(this.runningTasks.values()).filter(task => 
      task.startedAt && Date.now() - task.startedAt > task.timeout * 0.8
    );
    
    if (longRunningTasks.length > 0) {
      recommendations.push(`${longRunningTasks.length} tasks approaching timeout - monitor for potential issues`);
    }

    return recommendations;
  }

  /**
   * Get CPU health score (0-100)
   */
  getCPUHealthScore(): number {
    const currentStats = this.cpuHistory.length > 0 
      ? this.cpuHistory[this.cpuHistory.length - 1]
      : null;

    if (!currentStats) return 50; // Neutral score without data

    let score = 100;

    // Deduct for high CPU usage
    if (currentStats.usage > 95) {
      score -= 40;
    } else if (currentStats.usage > 85) {
      score -= 25;
    } else if (currentStats.usage > 75) {
      score -= 15;
    }

    // Deduct for high load average
    const loadRatio = currentStats.loadAverage[0] / os.cpus().length;
    if (loadRatio > 2) {
      score -= 30;
    } else if (loadRatio > 1.5) {
      score -= 20;
    } else if (loadRatio > 1) {
      score -= 10;
    }

    // Deduct for queue backlog
    if (this.taskQueue.length > 20) {
      score -= 20;
    } else if (this.taskQueue.length > 10) {
      score -= 10;
    }

    // Deduct for failed tasks
    const recentFailures = Array.from(this.runningTasks.values()).filter(task => 
      task.error && task.completedAt && Date.now() - task.completedAt < 300000 // Last 5 minutes
    ).length;
    
    score -= recentFailures * 5;

    return Math.max(0, score);
  }

  /**
   * Configure CPU optimization settings
   */
  configure(config: Partial<CPUOptimizationConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Restart components with new config
    this.stopOptimization();
    this.setupWorkerProcesses();
    this.startCPUMonitoring();
    this.startTaskScheduler();
    
    logger.info('CPU optimizer configuration updated', { config: this.config });
  }

  /**
   * Get default configuration
   */
  private getDefaultConfig(): CPUOptimizationConfig {
    return {
      enableWorkerProcesses: process.env.WCAG_ENABLE_WORKERS !== 'false',
      maxWorkers: parseInt(process.env.WCAG_MAX_WORKERS || String(Math.max(1, os.cpus().length - 1))),
      enableTaskQueue: true,
      enablePriorityScheduling: true,
      enableLoadBalancing: true,
      taskTimeout: parseInt(process.env.WCAG_TASK_TIMEOUT || '120000'), // 2 minutes
      maxRetries: parseInt(process.env.WCAG_MAX_RETRIES || '3'),
      cpuThreshold: parseInt(process.env.WCAG_CPU_THRESHOLD || '80'),
      loadAverageThreshold: parseFloat(process.env.WCAG_LOAD_THRESHOLD || '2.0'),
    };
  }

  /**
   * Setup worker processes
   */
  private setupWorkerProcesses(): void {
    if (!this.config.enableWorkerProcesses || cluster.isPrimary === false) {
      return;
    }

    // Initialize workers map
    this.workers.clear();

    // Create worker processes
    for (let i = 0; i < this.config.maxWorkers; i++) {
      this.createWorker();
    }

    // Handle worker events
    cluster.on('exit', (worker: cluster.Worker, code: number, signal: string) => {
      logger.warn(`Worker ${worker.process.pid} died`, { code, signal });
      this.workers.delete(worker.id);
      
      // Restart worker if not intentional shutdown
      if (code !== 0 && !worker.exitedAfterDisconnect) {
        this.createWorker();
      }
    });
  }

  /**
   * Create a new worker
   */
  private createWorker(): void {
    if (!this.config.enableWorkerProcesses) return;

    const worker = cluster.fork();
    
    this.workers.set(worker.id, {
      id: worker.id,
      pid: worker.process.pid!,
      status: 'idle',
      tasksCompleted: 0,
      totalCPUTime: 0,
      averageTaskTime: 0,
      lastActivity: Date.now(),
      memoryUsage: 0,
    });

    logger.debug(`Worker ${worker.id} created with PID ${worker.process.pid}`);
  }

  /**
   * Insert task into queue by priority
   */
  private insertTaskByPriority(task: WorkloadTask): void {
    const priorityOrder = { critical: 4, high: 3, normal: 2, low: 1 };
    const taskPriority = priorityOrder[task.priority];

    let insertIndex = this.taskQueue.length;
    
    for (let i = 0; i < this.taskQueue.length; i++) {
      const queuedTaskPriority = priorityOrder[this.taskQueue[i].priority];
      if (taskPriority > queuedTaskPriority) {
        insertIndex = i;
        break;
      }
    }

    this.taskQueue.splice(insertIndex, 0, task);
  }

  /**
   * Execute a task
   */
  private async executeTask(task: WorkloadTask): Promise<void> {
    task.startedAt = Date.now();
    this.runningTasks.set(task.id, task);

    // Remove from queue
    const queueIndex = this.taskQueue.findIndex(t => t.id === task.id);
    if (queueIndex >= 0) {
      this.taskQueue.splice(queueIndex, 1);
    }

    try {
      // Set timeout
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Task timeout')), task.timeout);
      });

      // Execute task with timeout
      await Promise.race([task.callback(), timeoutPromise]);
      
      task.completedAt = Date.now();
      
      logger.debug(`Task completed: ${task.id}`, {
        duration: task.completedAt - task.startedAt!,
        type: task.type,
      });

    } catch (error) {
      task.error = error as Error;
      task.completedAt = Date.now();
      
      // Retry if possible
      if (task.retries < task.maxRetries) {
        task.retries++;
        task.startedAt = undefined;
        task.error = undefined;
        
        // Re-queue for retry
        this.insertTaskByPriority(task);
        
        logger.warn(`Task failed, retrying: ${task.id}`, {
          error: error instanceof Error ? error.message : String(error),
          retries: task.retries,
          maxRetries: task.maxRetries,
        });
      } else {
        logger.error(`Task failed permanently: ${task.id}`, {
          error: error instanceof Error ? error.message : String(error),
          retries: task.retries,
        });
      }
    } finally {
      // Clean up completed/failed tasks after 5 minutes
      setTimeout(() => {
        this.runningTasks.delete(task.id);
      }, 300000);
    }
  }

  /**
   * Calculate CPU usage
   */
  private async calculateCPUUsage(): Promise<{ total: number; detailed: any }> {
    return new Promise((resolve) => {
      const startUsage = process.cpuUsage();
      const startTime = process.hrtime();

      setTimeout(() => {
        const endUsage = process.cpuUsage(startUsage);
        const endTime = process.hrtime(startTime);

        const totalTime = endTime[0] * 1000000 + endTime[1] / 1000; // microseconds
        const totalUsage = endUsage.user + endUsage.system;
        const usage = Math.min(100, (totalUsage / totalTime) * 100);

        resolve({
          total: usage,
          detailed: {
            user: (endUsage.user / totalTime) * 100,
            system: (endUsage.system / totalTime) * 100,
            idle: Math.max(0, 100 - usage),
            nice: 0,
            irq: 0,
          },
        });
      }, 100);
    });
  }

  /**
   * Start CPU monitoring
   */
  private startCPUMonitoring(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
    }

    this.monitoringInterval = setInterval(async () => {
      try {
        const stats = await this.getCurrentCPUStats();
        
        // Check thresholds
        if (stats.usage > this.config.cpuThreshold) {
          logger.warn('High CPU usage detected', {
            usage: stats.usage,
            threshold: this.config.cpuThreshold,
            loadAverage: stats.loadAverage[0],
          });
        }

        if (stats.loadAverage[0] > this.config.loadAverageThreshold) {
          logger.warn('High load average detected', {
            loadAverage: stats.loadAverage[0],
            threshold: this.config.loadAverageThreshold,
          });
        }

      } catch (error) {
        logger.error('Error during CPU monitoring', { error });
      }
    }, 30000); // Monitor every 30 seconds
  }

  /**
   * Start task scheduler
   */
  private startTaskScheduler(): void {
    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval);
    }

    this.schedulerInterval = setInterval(() => {
      try {
        if (this.taskQueue.length > 0) {
          const tasksToExecute = this.taskQueue.slice(0, this.getOptimalConcurrency());
          this.distributeWorkload(tasksToExecute);
        }
      } catch (error) {
        logger.error('Error during task scheduling', { error });
      }
    }, 5000); // Schedule every 5 seconds
  }

  /**
   * Stop CPU optimization
   */
  stopOptimization(): void {
    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    if (this.schedulerInterval) {
      clearInterval(this.schedulerInterval);
      this.schedulerInterval = null;
    }

    // Disconnect workers
    if (cluster.isPrimary) {
      for (const worker of Object.values(cluster.workers || {})) {
        if (worker) {
          worker.disconnect();
        }
      }
    }
  }

  /**
   * Get comprehensive CPU report
   */
  getCPUReport(): {
    currentStats: CPUStats | null;
    healthScore: number;
    recommendations: string[];
    taskQueue: { length: number; byPriority: Record<string, number> };
    runningTasks: { count: number; byType: Record<string, number> };
    workers: WorkerStats[];
    config: CPUOptimizationConfig;
  } {
    const currentStats = this.cpuHistory.length > 0 
      ? this.cpuHistory[this.cpuHistory.length - 1]
      : null;

    // Count tasks by priority
    const tasksByPriority = this.taskQueue.reduce((acc, task) => {
      acc[task.priority] = (acc[task.priority] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Count running tasks by type
    const runningTasksByType = Array.from(this.runningTasks.values()).reduce((acc, task) => {
      acc[task.type] = (acc[task.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return {
      currentStats,
      healthScore: this.getCPUHealthScore(),
      recommendations: this.getCPUOptimizationRecommendations(),
      taskQueue: {
        length: this.taskQueue.length,
        byPriority: tasksByPriority,
      },
      runningTasks: {
        count: this.runningTasks.size,
        byType: runningTasksByType,
      },
      workers: Array.from(this.workers.values()),
      config: this.config,
    };
  }
}

export default CPUOptimizer;
