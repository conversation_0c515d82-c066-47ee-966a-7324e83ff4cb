/**
 * Browser Pool Tests
 * Tests for the optimized browser pool implementation
 */

import BrowserPool from '../browser-pool';

describe('BrowserPool', () => {
  let browserPool: BrowserPool;

  beforeEach(() => {
    browserPool = BrowserPool.getInstance();
  });

  afterEach(async () => {
    await browserPool.shutdown();
  });

  describe('Page Management', () => {
    it('should create and manage pages efficiently', async () => {
      const scanId1 = 'test-scan-1';
      const scanId2 = 'test-scan-2';

      // Get first page
      const page1 = await browserPool.getPage(scanId1);
      expect(page1).toBeDefined();
      expect(page1.page).toBeDefined();
      expect(page1.isInUse).toBe(true);

      // Get second page
      const page2 = await browserPool.getPage(scanId2);
      expect(page2).toBeDefined();
      expect(page2.page).toBeDefined();
      expect(page2.isInUse).toBe(true);

      // Release first page
      await browserPool.releasePage(scanId1);

      // Get stats
      const stats = browserPool.getStats();
      expect(stats.inUsePages).toBe(1);
      expect(stats.availablePages).toBe(1);

      // Release second page
      await browserPool.releasePage(scanId2);

      const finalStats = browserPool.getStats();
      expect(finalStats.inUsePages).toBe(0);
      expect(finalStats.availablePages).toBe(2);
    }, 30000);

    it('should reuse pages efficiently', async () => {
      const scanId1 = 'test-scan-1';
      const scanId2 = 'test-scan-2';

      // Get and release first page
      const page1 = await browserPool.getPage(scanId1);
      await browserPool.releasePage(scanId1);

      // Get second page (should reuse)
      const page2 = await browserPool.getPage(scanId2);
      
      // Should be the same page instance
      expect(page2.page).toBe(page1.page);
      expect(page2.scanCount).toBe(2); // Incremented from reuse

      await browserPool.releasePage(scanId2);
    }, 30000);

    it('should handle concurrent page requests', async () => {
      const scanIds = ['scan-1', 'scan-2', 'scan-3'];
      
      // Request multiple pages concurrently
      const pagePromises = scanIds.map(scanId => browserPool.getPage(scanId));
      const pages = await Promise.all(pagePromises);

      // All pages should be different
      expect(pages).toHaveLength(3);
      expect(new Set(pages.map(p => p.page)).size).toBe(3);

      // Release all pages
      await Promise.all(scanIds.map(scanId => browserPool.releasePage(scanId)));

      const stats = browserPool.getStats();
      expect(stats.inUsePages).toBe(0);
      expect(stats.availablePages).toBe(3);
    }, 30000);
  });

  describe('Memory Management', () => {
    it('should track memory usage', async () => {
      const scanId = 'memory-test';
      const page = await browserPool.getPage(scanId);

      const stats = browserPool.getStats();
      expect(stats.memoryUsageMB).toBeGreaterThan(0);

      await browserPool.releasePage(scanId);
    }, 30000);

    it('should handle page recycling after many uses', async () => {
      const scanId = 'recycle-test';
      
      // Use the same page many times to trigger recycling
      for (let i = 0; i < 12; i++) {
        const page = await browserPool.getPage(`${scanId}-${i}`);
        await browserPool.releasePage(`${scanId}-${i}`);
      }

      const stats = browserPool.getStats();
      expect(stats.availablePages).toBeGreaterThan(0);
    }, 60000);
  });

  describe('Error Handling', () => {
    it('should handle invalid scan IDs gracefully', async () => {
      // Try to release a non-existent scan
      await expect(browserPool.releasePage('non-existent')).resolves.not.toThrow();
    });

    it('should handle browser disconnection', async () => {
      const scanId = 'disconnect-test';
      const page = await browserPool.getPage(scanId);
      
      // Force close the browser
      await page.browser.close();
      
      // Should still handle release gracefully
      await expect(browserPool.releasePage(scanId)).resolves.not.toThrow();
    }, 30000);
  });

  describe('Performance Monitoring', () => {
    it('should provide accurate statistics', async () => {
      const initialStats = browserPool.getStats();
      
      const scanId = 'stats-test';
      await browserPool.getPage(scanId);
      
      const activeStats = browserPool.getStats();
      expect(activeStats.inUsePages).toBe(initialStats.inUsePages + 1);
      expect(activeStats.totalPages).toBe(initialStats.totalPages + 1);
      
      await browserPool.releasePage(scanId);
      
      const finalStats = browserPool.getStats();
      expect(finalStats.inUsePages).toBe(initialStats.inUsePages);
      expect(finalStats.availablePages).toBe(initialStats.availablePages + 1);
    }, 30000);
  });

  describe('Cleanup and Shutdown', () => {
    it('should shutdown cleanly', async () => {
      const scanId = 'shutdown-test';
      await browserPool.getPage(scanId);
      
      // Should shutdown without errors
      await expect(browserPool.shutdown()).resolves.not.toThrow();
      
      const stats = browserPool.getStats();
      expect(stats.totalBrowsers).toBe(0);
      expect(stats.totalPages).toBe(0);
    }, 30000);
  });
});

describe('BrowserPool Integration', () => {
  it('should work with real page navigation', async () => {
    const browserPool = BrowserPool.getInstance();
    const scanId = 'integration-test';
    
    try {
      const pageInstance = await browserPool.getPage(scanId);
      
      // Navigate to a test page
      await pageInstance.page.goto('data:text/html,<html><body><h1>Test Page</h1></body></html>');
      
      // Verify navigation worked
      const title = await pageInstance.page.$eval('h1', el => el.textContent);
      expect(title).toBe('Test Page');
      
      await browserPool.releasePage(scanId);
    } finally {
      await browserPool.shutdown();
    }
  }, 30000);

  it('should handle multiple concurrent navigations', async () => {
    const browserPool = BrowserPool.getInstance();
    const scanIds = ['nav-1', 'nav-2', 'nav-3'];
    
    try {
      const pages = await Promise.all(
        scanIds.map(scanId => browserPool.getPage(scanId))
      );
      
      // Navigate all pages concurrently
      await Promise.all(
        pages.map((pageInstance, index) =>
          pageInstance.page.goto(`data:text/html,<html><body><h1>Page ${index + 1}</h1></body></html>`)
        )
      );
      
      // Verify all navigations
      const titles = await Promise.all(
        pages.map(pageInstance =>
          pageInstance.page.$eval('h1', el => el.textContent)
        )
      );
      
      expect(titles).toEqual(['Page 1', 'Page 2', 'Page 3']);
      
      // Release all pages
      await Promise.all(
        scanIds.map(scanId => browserPool.releasePage(scanId))
      );
    } finally {
      await browserPool.shutdown();
    }
  }, 45000);
});
