/**
 * WCAG Performance Monitor
 * Tracks scan performance metrics and provides optimization insights
 */

import logger from '../../../utils/logger';

export interface ScanPerformanceMetrics {
  scanId: string;
  startTime: Date;
  endTime?: Date;
  totalDuration?: number;
  checkMetrics: Map<string, CheckPerformanceMetric>;
  memoryMetrics: MemoryMetric[];
  browserMetrics: BrowserMetric;
  overallStats: OverallPerformanceStats;
}

export interface CheckPerformanceMetric {
  ruleId: string;
  ruleName: string;
  startTime: Date;
  endTime?: Date;
  duration?: number;
  memoryUsedMB: number;
  success: boolean;
  errorMessage?: string;
}

export interface MemoryMetric {
  timestamp: Date;
  heapUsedMB: number;
  heapTotalMB: number;
  rssMB: number;
  externalMB: number;
}

export interface BrowserMetric {
  pagesCreated: number;
  pagesReused: number;
  browsersCreated: number;
  browsersReused: number;
  poolEfficiency: number;
}

export interface OverallPerformanceStats {
  averageCheckDuration: number;
  slowestCheck: string;
  fastestCheck: string;
  memoryPeakMB: number;
  memoryAverageMB: number;
  totalChecksExecuted: number;
  successfulChecks: number;
  failedChecks: number;
  performanceScore: number; // 0-100
}

export interface PerformanceReport {
  scanId: string;
  metrics: ScanPerformanceMetrics;
  recommendations: string[];
  comparisonToBaseline?: PerformanceComparison;
}

export interface PerformanceComparison {
  durationImprovement: number; // percentage
  memoryImprovement: number; // percentage
  efficiencyImprovement: number; // percentage
}

/**
 * Enhanced performance monitoring for WCAG scans
 */
export class WCAGPerformanceMonitor {
  private static instance: WCAGPerformanceMonitor;
  private activeScans = new Map<string, ScanPerformanceMetrics>();
  private historicalData: ScanPerformanceMetrics[] = [];
  private baselineMetrics: OverallPerformanceStats | null = null;

  private constructor() {
    // Start memory monitoring
    this.startMemoryMonitoring();
  }

  static getInstance(): WCAGPerformanceMonitor {
    if (!WCAGPerformanceMonitor.instance) {
      WCAGPerformanceMonitor.instance = new WCAGPerformanceMonitor();
    }
    return WCAGPerformanceMonitor.instance;
  }

  /**
   * Start monitoring a scan
   */
  startScanMonitoring(scanId: string): void {
    const metrics: ScanPerformanceMetrics = {
      scanId,
      startTime: new Date(),
      checkMetrics: new Map(),
      memoryMetrics: [],
      browserMetrics: {
        pagesCreated: 0,
        pagesReused: 0,
        browsersCreated: 0,
        browsersReused: 0,
        poolEfficiency: 0,
      },
      overallStats: {
        averageCheckDuration: 0,
        slowestCheck: '',
        fastestCheck: '',
        memoryPeakMB: 0,
        memoryAverageMB: 0,
        totalChecksExecuted: 0,
        successfulChecks: 0,
        failedChecks: 0,
        performanceScore: 100,
      },
    };

    this.activeScans.set(scanId, metrics);
    logger.debug(`📊 Started performance monitoring for scan: ${scanId}`);
  }

  /**
   * Record check performance
   */
  recordCheckStart(scanId: string, ruleId: string, ruleName: string): void {
    const metrics = this.activeScans.get(scanId);
    if (!metrics) return;

    const checkMetric: CheckPerformanceMetric = {
      ruleId,
      ruleName,
      startTime: new Date(),
      memoryUsedMB: this.getCurrentMemoryUsageMB(),
      success: false,
    };

    metrics.checkMetrics.set(ruleId, checkMetric);
  }

  /**
   * Record check completion
   */
  recordCheckEnd(scanId: string, ruleId: string, success: boolean, errorMessage?: string): void {
    const metrics = this.activeScans.get(scanId);
    if (!metrics) return;

    const checkMetric = metrics.checkMetrics.get(ruleId);
    if (!checkMetric) return;

    checkMetric.endTime = new Date();
    checkMetric.duration = checkMetric.endTime.getTime() - checkMetric.startTime.getTime();
    checkMetric.success = success;
    checkMetric.errorMessage = errorMessage;

    // Update overall stats
    metrics.overallStats.totalChecksExecuted++;
    if (success) {
      metrics.overallStats.successfulChecks++;
    } else {
      metrics.overallStats.failedChecks++;
    }

    logger.debug(`⏱️ Check ${ruleId} completed in ${checkMetric.duration}ms (success: ${success})`);
  }

  /**
   * Record browser pool metrics
   */
  recordBrowserMetrics(scanId: string, metrics: Partial<BrowserMetric>): void {
    const scanMetrics = this.activeScans.get(scanId);
    if (!scanMetrics) return;

    Object.assign(scanMetrics.browserMetrics, metrics);
    
    // Calculate pool efficiency
    const total = scanMetrics.browserMetrics.pagesCreated + scanMetrics.browserMetrics.pagesReused;
    if (total > 0) {
      scanMetrics.browserMetrics.poolEfficiency = 
        (scanMetrics.browserMetrics.pagesReused / total) * 100;
    }
  }

  /**
   * Complete scan monitoring and generate report
   */
  completeScanMonitoring(scanId: string): PerformanceReport {
    const metrics = this.activeScans.get(scanId);
    if (!metrics) {
      throw new Error(`No metrics found for scan: ${scanId}`);
    }

    metrics.endTime = new Date();
    metrics.totalDuration = metrics.endTime.getTime() - metrics.startTime.getTime();

    // Calculate final statistics
    this.calculateFinalStats(metrics);

    // Generate recommendations
    const recommendations = this.generateRecommendations(metrics);

    // Compare to baseline if available
    const comparisonToBaseline = this.baselineMetrics 
      ? this.compareToBaseline(metrics.overallStats)
      : undefined;

    // Store in historical data
    this.historicalData.push(metrics);
    this.activeScans.delete(scanId);

    // Update baseline if this is better
    this.updateBaseline(metrics.overallStats);

    const report: PerformanceReport = {
      scanId,
      metrics,
      recommendations,
      comparisonToBaseline,
    };

    logger.info(`📊 Performance report generated for scan: ${scanId}`, {
      duration: metrics.totalDuration,
      checksExecuted: metrics.overallStats.totalChecksExecuted,
      successRate: (metrics.overallStats.successfulChecks / metrics.overallStats.totalChecksExecuted) * 100,
      memoryPeak: metrics.overallStats.memoryPeakMB,
      performanceScore: metrics.overallStats.performanceScore,
    });

    return report;
  }

  /**
   * Calculate final performance statistics
   */
  private calculateFinalStats(metrics: ScanPerformanceMetrics): void {
    const checkDurations = Array.from(metrics.checkMetrics.values())
      .filter(check => check.duration !== undefined)
      .map(check => check.duration!);

    if (checkDurations.length > 0) {
      metrics.overallStats.averageCheckDuration = 
        checkDurations.reduce((sum, duration) => sum + duration, 0) / checkDurations.length;

      const sortedDurations = checkDurations.sort((a, b) => a - b);
      const slowestCheckId = Array.from(metrics.checkMetrics.entries())
        .find(([_, check]) => check.duration === sortedDurations[sortedDurations.length - 1])?.[0] || '';
      const fastestCheckId = Array.from(metrics.checkMetrics.entries())
        .find(([_, check]) => check.duration === sortedDurations[0])?.[0] || '';

      metrics.overallStats.slowestCheck = slowestCheckId;
      metrics.overallStats.fastestCheck = fastestCheckId;
    }

    // Calculate memory statistics
    if (metrics.memoryMetrics.length > 0) {
      const memoryValues = metrics.memoryMetrics.map(m => m.heapUsedMB);
      metrics.overallStats.memoryPeakMB = Math.max(...memoryValues);
      metrics.overallStats.memoryAverageMB = 
        memoryValues.reduce((sum, val) => sum + val, 0) / memoryValues.length;
    }

    // Calculate performance score (0-100)
    metrics.overallStats.performanceScore = this.calculatePerformanceScore(metrics);
  }

  /**
   * Calculate overall performance score
   */
  private calculatePerformanceScore(metrics: ScanPerformanceMetrics): number {
    let score = 100;

    // Deduct points for slow performance
    if (metrics.totalDuration! > 60000) { // > 1 minute
      score -= 20;
    } else if (metrics.totalDuration! > 30000) { // > 30 seconds
      score -= 10;
    }

    // Deduct points for high memory usage
    if (metrics.overallStats.memoryPeakMB > 2000) { // > 2GB
      score -= 20;
    } else if (metrics.overallStats.memoryPeakMB > 1000) { // > 1GB
      score -= 10;
    }

    // Deduct points for failed checks
    const failureRate = metrics.overallStats.failedChecks / metrics.overallStats.totalChecksExecuted;
    score -= failureRate * 30;

    // Add points for browser pool efficiency
    if (metrics.browserMetrics.poolEfficiency > 80) {
      score += 5;
    } else if (metrics.browserMetrics.poolEfficiency > 60) {
      score += 2;
    }

    return Math.max(0, Math.min(100, score));
  }

  /**
   * Generate performance recommendations
   */
  private generateRecommendations(metrics: ScanPerformanceMetrics): string[] {
    const recommendations: string[] = [];

    // Duration recommendations
    if (metrics.totalDuration! > 60000) {
      recommendations.push('Consider enabling more aggressive caching to reduce scan time');
      recommendations.push('Optimize slow checks: ' + metrics.overallStats.slowestCheck);
    }

    // Memory recommendations
    if (metrics.overallStats.memoryPeakMB > 1500) {
      recommendations.push('High memory usage detected - consider reducing concurrent checks');
      recommendations.push('Enable garbage collection optimization');
    }

    // Browser pool recommendations
    if (metrics.browserMetrics.poolEfficiency < 50) {
      recommendations.push('Low browser pool efficiency - consider increasing pool size');
    }

    // Check failure recommendations
    if (metrics.overallStats.failedChecks > 0) {
      recommendations.push(`${metrics.overallStats.failedChecks} checks failed - review error logs`);
    }

    // Performance score recommendations
    if (metrics.overallStats.performanceScore < 70) {
      recommendations.push('Overall performance is below optimal - consider system optimization');
    }

    return recommendations;
  }

  /**
   * Compare performance to baseline
   */
  private compareToBaseline(stats: OverallPerformanceStats): PerformanceComparison {
    if (!this.baselineMetrics) {
      return { durationImprovement: 0, memoryImprovement: 0, efficiencyImprovement: 0 };
    }

    const durationImprovement = 
      ((this.baselineMetrics.averageCheckDuration - stats.averageCheckDuration) / 
       this.baselineMetrics.averageCheckDuration) * 100;

    const memoryImprovement = 
      ((this.baselineMetrics.memoryPeakMB - stats.memoryPeakMB) / 
       this.baselineMetrics.memoryPeakMB) * 100;

    const efficiencyImprovement = 
      ((stats.performanceScore - this.baselineMetrics.performanceScore) / 
       this.baselineMetrics.performanceScore) * 100;

    return {
      durationImprovement: Math.round(durationImprovement * 100) / 100,
      memoryImprovement: Math.round(memoryImprovement * 100) / 100,
      efficiencyImprovement: Math.round(efficiencyImprovement * 100) / 100,
    };
  }

  /**
   * Update baseline metrics if current performance is better
   */
  private updateBaseline(stats: OverallPerformanceStats): void {
    if (!this.baselineMetrics || stats.performanceScore > this.baselineMetrics.performanceScore) {
      this.baselineMetrics = { ...stats };
      logger.info('📈 New performance baseline established', {
        performanceScore: stats.performanceScore,
        averageCheckDuration: stats.averageCheckDuration,
        memoryPeakMB: stats.memoryPeakMB,
      });
    }
  }

  /**
   * Start memory monitoring interval
   */
  private startMemoryMonitoring(): void {
    setInterval(() => {
      const memoryMetric: MemoryMetric = {
        timestamp: new Date(),
        ...this.getCurrentMemoryUsage(),
      };

      // Add to all active scans
      for (const metrics of this.activeScans.values()) {
        metrics.memoryMetrics.push(memoryMetric);
      }
    }, 5000); // Every 5 seconds
  }

  /**
   * Get current memory usage
   */
  private getCurrentMemoryUsage(): MemoryMetric {
    const usage = process.memoryUsage();
    return {
      timestamp: new Date(),
      heapUsedMB: Math.round(usage.heapUsed / 1024 / 1024),
      heapTotalMB: Math.round(usage.heapTotal / 1024 / 1024),
      rssMB: Math.round(usage.rss / 1024 / 1024),
      externalMB: Math.round(usage.external / 1024 / 1024),
    };
  }

  /**
   * Get current memory usage in MB
   */
  private getCurrentMemoryUsageMB(): number {
    return Math.round(process.memoryUsage().heapUsed / 1024 / 1024);
  }

  /**
   * Get historical performance data
   */
  getHistoricalData(): ScanPerformanceMetrics[] {
    return [...this.historicalData];
  }

  /**
   * Get current baseline metrics
   */
  getBaselineMetrics(): OverallPerformanceStats | null {
    return this.baselineMetrics ? { ...this.baselineMetrics } : null;
  }

  /**
   * Reset baseline metrics
   */
  resetBaseline(): void {
    this.baselineMetrics = null;
    logger.info('📊 Performance baseline reset');
  }
}

export default WCAGPerformanceMonitor;
