/**
 * E-commerce Site Optimizer for WCAG Scanning
 * Specialized accessibility analysis for shopping carts, product catalogs, checkout flows, and payment forms
 */

import { Page } from 'puppeteer';
import logger from '../../../utils/logger';

export interface EcommerceComponent {
  type: 'product-listing' | 'product-detail' | 'shopping-cart' | 'checkout' | 'payment' | 'search' | 'filter' | 'review';
  selector: string;
  isAccessible: boolean;
  accessibilityScore: number; // 0-100
  issues: string[];
  recommendations: string[];
  criticalElements: {
    priceDisplay: boolean;
    addToCartButton: boolean;
    quantitySelector: boolean;
    productImages: boolean;
    productDescription: boolean;
  };
}

export interface EcommerceAnalysisResult {
  platform: string; // Shopify, WooCommerce, Magento, etc.
  components: EcommerceComponent[];
  checkoutFlow: {
    steps: string[];
    accessibilityIssues: string[];
    recommendations: string[];
  };
  paymentSecurity: {
    hasSecurePayment: boolean;
    accessibilityIssues: string[];
  };
  productAccessibility: {
    totalProducts: number;
    accessibleProducts: number;
    commonIssues: string[];
  };
  overallScore: number;
  criticalIssues: string[];
  optimizations: string[];
}

export interface EcommerceOptimizationConfig {
  analyzeProductListings: boolean;
  analyzeProductDetails: boolean;
  analyzeShoppingCart: boolean;
  analyzeCheckoutFlow: boolean;
  analyzePaymentForms: boolean;
  analyzeSearchAndFilters: boolean;
  analyzeReviews: boolean;
  testCheckoutProcess: boolean;
  deepAnalysis: boolean;
}

/**
 * Advanced e-commerce accessibility optimizer
 */
export class EcommerceOptimizer {
  private static instance: EcommerceOptimizer;

  private constructor() {}

  static getInstance(): EcommerceOptimizer {
    if (!EcommerceOptimizer.instance) {
      EcommerceOptimizer.instance = new EcommerceOptimizer();
    }
    return EcommerceOptimizer.instance;
  }

  /**
   * Analyze e-commerce site accessibility
   */
  async analyzeEcommerceSite(page: Page, config: Partial<EcommerceOptimizationConfig> = {}): Promise<EcommerceAnalysisResult> {
    const fullConfig: EcommerceOptimizationConfig = {
      analyzeProductListings: config.analyzeProductListings ?? true,
      analyzeProductDetails: config.analyzeProductDetails ?? true,
      analyzeShoppingCart: config.analyzeShoppingCart ?? true,
      analyzeCheckoutFlow: config.analyzeCheckoutFlow ?? true,
      analyzePaymentForms: config.analyzePaymentForms ?? true,
      analyzeSearchAndFilters: config.analyzeSearchAndFilters ?? true,
      analyzeReviews: config.analyzeReviews ?? true,
      testCheckoutProcess: config.testCheckoutProcess ?? false, // Disabled by default for safety
      deepAnalysis: config.deepAnalysis ?? true,
    };

    logger.debug('🛒 Starting e-commerce accessibility analysis');

    // Inject e-commerce analysis functions
    await this.injectEcommerceAnalysisFunctions(page);

    // Detect e-commerce platform
    const platform = await this.detectEcommercePlatform(page);

    // Analyze components
    const components: EcommerceComponent[] = [];

    if (fullConfig.analyzeProductListings) {
      const productListings = await this.analyzeProductListings(page);
      components.push(...productListings);
    }

    if (fullConfig.analyzeProductDetails) {
      const productDetails = await this.analyzeProductDetails(page);
      components.push(...productDetails);
    }

    if (fullConfig.analyzeShoppingCart) {
      const shoppingCart = await this.analyzeShoppingCart(page);
      components.push(...shoppingCart);
    }

    if (fullConfig.analyzeCheckoutFlow) {
      const checkout = await this.analyzeCheckoutFlow(page);
      components.push(...checkout);
    }

    if (fullConfig.analyzePaymentForms) {
      const payment = await this.analyzePaymentForms(page);
      components.push(...payment);
    }

    if (fullConfig.analyzeSearchAndFilters) {
      const searchFilters = await this.analyzeSearchAndFilters(page);
      components.push(...searchFilters);
    }

    if (fullConfig.analyzeReviews) {
      const reviews = await this.analyzeReviews(page);
      components.push(...reviews);
    }

    // Analyze checkout flow
    const checkoutFlow = await this.analyzeCheckoutFlowAccessibility(page);

    // Analyze payment security
    const paymentSecurity = await this.analyzePaymentSecurity(page);

    // Analyze product accessibility
    const productAccessibility = await this.analyzeProductAccessibility(page);

    // Calculate overall results
    const result = this.calculateOverallResults(platform, components, checkoutFlow, paymentSecurity, productAccessibility);

    logger.info(`✅ E-commerce analysis completed: ${platform} (score: ${result.overallScore})`, {
      components: components.length,
      criticalIssues: result.criticalIssues.length,
      accessibleProducts: `${productAccessibility.accessibleProducts}/${productAccessibility.totalProducts}`,
    });

    return result;
  }

  /**
   * Inject e-commerce analysis functions
   */
  private async injectEcommerceAnalysisFunctions(page: Page): Promise<void> {
    await page.evaluateOnNewDocument(() => {
      (window as any).wcagEcommerceAnalysis = {
        
        /**
         * Get element selector
         */
        getElementSelector(element: HTMLElement): string {
          if (element.id) return `#${element.id}`;
          
          const path = [];
          let current = element;
          
          while (current && current.nodeType === Node.ELEMENT_NODE && current !== document.body) {
            let selector = current.nodeName.toLowerCase();
            
            if (current.className) {
              const classes = current.className.split(' ').filter(c => c.trim());
              if (classes.length > 0) {
                selector += '.' + classes.slice(0, 2).join('.');
              }
            }
            
            path.unshift(selector);
            current = current.parentElement!;
            
            if (path.length > 4) break;
          }
          
          return path.join(' > ');
        },

        /**
         * Check if element is accessible
         */
        isElementAccessible(element: HTMLElement): { accessible: boolean; issues: string[] } {
          const issues: string[] = [];
          
          // Check for alt text on images
          if (element.tagName === 'IMG' && !element.getAttribute('alt')) {
            issues.push('Image missing alt text');
          }
          
          // Check for proper labeling on form elements
          if (['INPUT', 'SELECT', 'TEXTAREA'].includes(element.tagName)) {
            const hasLabel = element.getAttribute('aria-label') || 
                           element.getAttribute('aria-labelledby') ||
                           document.querySelector(`label[for="${element.id}"]`);
            if (!hasLabel) {
              issues.push('Form element missing label');
            }
          }
          
          // Check for proper button labeling
          if (element.tagName === 'BUTTON' || element.getAttribute('role') === 'button') {
            const hasLabel = element.textContent?.trim() || 
                           element.getAttribute('aria-label') ||
                           element.getAttribute('title');
            if (!hasLabel) {
              issues.push('Button missing accessible label');
            }
          }
          
          // Check for keyboard accessibility
          const isInteractive = ['BUTTON', 'A', 'INPUT', 'SELECT', 'TEXTAREA'].includes(element.tagName) ||
                               element.getAttribute('role') === 'button' ||
                               element.getAttribute('tabindex') !== null;
          
          if (isInteractive && element.getAttribute('tabindex') === '-1') {
            issues.push('Interactive element not keyboard accessible');
          }
          
          return {
            accessible: issues.length === 0,
            issues,
          };
        },

        /**
         * Analyze critical e-commerce elements
         */
        analyzeCriticalElements(container: HTMLElement): any {
          const criticalElements = {
            priceDisplay: false,
            addToCartButton: false,
            quantitySelector: false,
            productImages: false,
            productDescription: false,
          };

          // Check for price display
          const priceSelectors = ['.price', '[class*="price"]', '[data-price]', '.cost', '.amount'];
          priceSelectors.forEach(selector => {
            if (container.querySelector(selector)) {
              criticalElements.priceDisplay = true;
            }
          });

          // Check for add to cart button
          const cartSelectors = ['[class*="add-to-cart"]', '[class*="addtocart"]', 'button[type="submit"]'];
          cartSelectors.forEach(selector => {
            if (container.querySelector(selector)) {
              criticalElements.addToCartButton = true;
            }
          });

          // Check for quantity selector
          const quantitySelectors = ['input[name*="quantity"]', '.quantity', '[class*="qty"]'];
          quantitySelectors.forEach(selector => {
            if (container.querySelector(selector)) {
              criticalElements.quantitySelector = true;
            }
          });

          // Check for product images
          if (container.querySelector('img')) {
            criticalElements.productImages = true;
          }

          // Check for product description
          const descSelectors = ['.description', '[class*="desc"]', '.product-content', '.details'];
          descSelectors.forEach(selector => {
            if (container.querySelector(selector)) {
              criticalElements.productDescription = true;
            }
          });

          return criticalElements;
        },

        /**
         * Calculate accessibility score
         */
        calculateAccessibilityScore(issues: string[], criticalElements: any): number {
          let score = 100;
          
          // Deduct points for each issue
          score -= issues.length * 10;
          
          // Deduct points for missing critical elements
          const criticalCount = Object.values(criticalElements).filter(Boolean).length;
          const totalCritical = Object.keys(criticalElements).length;
          const criticalScore = (criticalCount / totalCritical) * 20;
          score = score - 20 + criticalScore;
          
          return Math.max(0, Math.min(100, score));
        },

        /**
         * Generate recommendations
         */
        generateRecommendations(issues: string[], criticalElements: any): string[] {
          const recommendations: string[] = [];
          
          if (issues.includes('Image missing alt text')) {
            recommendations.push('Add descriptive alt text to all product images');
          }
          
          if (issues.includes('Form element missing label')) {
            recommendations.push('Add proper labels to all form elements');
          }
          
          if (issues.includes('Button missing accessible label')) {
            recommendations.push('Ensure all buttons have descriptive labels');
          }
          
          if (!criticalElements.priceDisplay) {
            recommendations.push('Ensure price information is clearly displayed and accessible');
          }
          
          if (!criticalElements.addToCartButton) {
            recommendations.push('Provide clear and accessible add to cart functionality');
          }
          
          return recommendations;
        }
      };
    });
  }

  /**
   * Detect e-commerce platform
   */
  private async detectEcommercePlatform(page: Page): Promise<string> {
    return await page.evaluate(() => {
      // Shopify detection
      if (document.querySelector('script[src*="cdn.shopify.com"]') || 
          (window as any).Shopify) {
        return 'Shopify';
      }
      
      // WooCommerce detection
      if (document.querySelector('body[class*="woocommerce"]') || 
          document.querySelector('[class*="woocommerce"]')) {
        return 'WooCommerce';
      }
      
      // Magento detection
      if (document.querySelector('script[src*="mage"]') || 
          document.querySelector('body[class*="magento"]')) {
        return 'Magento';
      }
      
      // BigCommerce detection
      if (document.querySelector('script[src*="bigcommerce"]') || 
          (window as any).BigCommerce) {
        return 'BigCommerce';
      }
      
      // PrestaShop detection
      if (document.querySelector('meta[name="generator"][content*="PrestaShop"]')) {
        return 'PrestaShop';
      }
      
      // Generic e-commerce indicators
      if (document.querySelector('[class*="cart"]') && 
          document.querySelector('[class*="product"]') && 
          document.querySelector('[class*="price"]')) {
        return 'Generic E-commerce';
      }
      
      return 'Unknown';
    });
  }

  /**
   * Analyze product listings
   */
  private async analyzeProductListings(page: Page): Promise<EcommerceComponent[]> {
    return await page.evaluate(() => {
      const components: EcommerceComponent[] = [];
      
      // Common product listing selectors
      const listingSelectors = [
        '.product-list',
        '.products',
        '[class*="product-grid"]',
        '[class*="product-listing"]',
        '.shop-products',
        '.catalog-products',
      ];
      
      listingSelectors.forEach(selector => {
        const listings = document.querySelectorAll(selector);
        listings.forEach(listing => {
          const element = listing as HTMLElement;
          const analysis = (window as any).wcagEcommerceAnalysis;
          const accessibility = analysis.isElementAccessible(element);
          const criticalElements = analysis.analyzeCriticalElements(element);
          
          const component: EcommerceComponent = {
            type: 'product-listing',
            selector: analysis.getElementSelector(element),
            isAccessible: accessibility.accessible,
            accessibilityScore: analysis.calculateAccessibilityScore(accessibility.issues, criticalElements),
            issues: accessibility.issues,
            recommendations: analysis.generateRecommendations(accessibility.issues, criticalElements),
            criticalElements,
          };
          
          components.push(component);
        });
      });
      
      return components;
    });
  }

  /**
   * Analyze product details
   */
  private async analyzeProductDetails(page: Page): Promise<EcommerceComponent[]> {
    return await page.evaluate(() => {
      const components: EcommerceComponent[] = [];
      
      const detailSelectors = [
        '.product-detail',
        '.product-page',
        '[class*="product-single"]',
        '.single-product',
        '.product-view',
      ];
      
      detailSelectors.forEach(selector => {
        const details = document.querySelectorAll(selector);
        details.forEach(detail => {
          const element = detail as HTMLElement;
          const analysis = (window as any).wcagEcommerceAnalysis;
          const accessibility = analysis.isElementAccessible(element);
          const criticalElements = analysis.analyzeCriticalElements(element);
          
          const component: EcommerceComponent = {
            type: 'product-detail',
            selector: analysis.getElementSelector(element),
            isAccessible: accessibility.accessible,
            accessibilityScore: analysis.calculateAccessibilityScore(accessibility.issues, criticalElements),
            issues: accessibility.issues,
            recommendations: analysis.generateRecommendations(accessibility.issues, criticalElements),
            criticalElements,
          };
          
          components.push(component);
        });
      });
      
      return components;
    });
  }

  /**
   * Analyze shopping cart
   */
  private async analyzeShoppingCart(page: Page): Promise<EcommerceComponent[]> {
    return await page.evaluate(() => {
      const components: EcommerceComponent[] = [];
      
      const cartSelectors = [
        '.shopping-cart',
        '.cart',
        '[class*="mini-cart"]',
        '.cart-drawer',
        '#cart',
      ];
      
      cartSelectors.forEach(selector => {
        const carts = document.querySelectorAll(selector);
        carts.forEach(cart => {
          const element = cart as HTMLElement;
          const analysis = (window as any).wcagEcommerceAnalysis;
          const accessibility = analysis.isElementAccessible(element);
          const criticalElements = analysis.analyzeCriticalElements(element);
          
          const component: EcommerceComponent = {
            type: 'shopping-cart',
            selector: analysis.getElementSelector(element),
            isAccessible: accessibility.accessible,
            accessibilityScore: analysis.calculateAccessibilityScore(accessibility.issues, criticalElements),
            issues: accessibility.issues,
            recommendations: analysis.generateRecommendations(accessibility.issues, criticalElements),
            criticalElements,
          };
          
          components.push(component);
        });
      });
      
      return components;
    });
  }

  /**
   * Analyze checkout flow
   */
  private async analyzeCheckoutFlow(page: Page): Promise<EcommerceComponent[]> {
    return await page.evaluate(() => {
      const components: EcommerceComponent[] = [];
      
      const checkoutSelectors = [
        '.checkout',
        '.checkout-form',
        '[class*="checkout"]',
        '.order-form',
        '#checkout',
      ];
      
      checkoutSelectors.forEach(selector => {
        const checkouts = document.querySelectorAll(selector);
        checkouts.forEach(checkout => {
          const element = checkout as HTMLElement;
          const analysis = (window as any).wcagEcommerceAnalysis;
          const accessibility = analysis.isElementAccessible(element);
          const criticalElements = analysis.analyzeCriticalElements(element);
          
          const component: EcommerceComponent = {
            type: 'checkout',
            selector: analysis.getElementSelector(element),
            isAccessible: accessibility.accessible,
            accessibilityScore: analysis.calculateAccessibilityScore(accessibility.issues, criticalElements),
            issues: accessibility.issues,
            recommendations: analysis.generateRecommendations(accessibility.issues, criticalElements),
            criticalElements,
          };
          
          components.push(component);
        });
      });
      
      return components;
    });
  }

  /**
   * Analyze payment forms
   */
  private async analyzePaymentForms(page: Page): Promise<EcommerceComponent[]> {
    return await page.evaluate(() => {
      const components: EcommerceComponent[] = [];
      
      const paymentSelectors = [
        '.payment-form',
        '[class*="payment"]',
        '.billing-form',
        '.credit-card-form',
        '#payment',
      ];
      
      paymentSelectors.forEach(selector => {
        const payments = document.querySelectorAll(selector);
        payments.forEach(payment => {
          const element = payment as HTMLElement;
          const analysis = (window as any).wcagEcommerceAnalysis;
          const accessibility = analysis.isElementAccessible(element);
          const criticalElements = analysis.analyzeCriticalElements(element);
          
          const component: EcommerceComponent = {
            type: 'payment',
            selector: analysis.getElementSelector(element),
            isAccessible: accessibility.accessible,
            accessibilityScore: analysis.calculateAccessibilityScore(accessibility.issues, criticalElements),
            issues: accessibility.issues,
            recommendations: analysis.generateRecommendations(accessibility.issues, criticalElements),
            criticalElements,
          };
          
          components.push(component);
        });
      });
      
      return components;
    });
  }

  /**
   * Analyze search and filters
   */
  private async analyzeSearchAndFilters(page: Page): Promise<EcommerceComponent[]> {
    return await page.evaluate(() => {
      const components: EcommerceComponent[] = [];
      
      const searchSelectors = [
        '.search-form',
        '.product-search',
        '[class*="filter"]',
        '.facets',
        '.search-filters',
      ];
      
      searchSelectors.forEach(selector => {
        const searches = document.querySelectorAll(selector);
        searches.forEach(search => {
          const element = search as HTMLElement;
          const analysis = (window as any).wcagEcommerceAnalysis;
          const accessibility = analysis.isElementAccessible(element);
          const criticalElements = analysis.analyzeCriticalElements(element);
          
          const component: EcommerceComponent = {
            type: 'search',
            selector: analysis.getElementSelector(element),
            isAccessible: accessibility.accessible,
            accessibilityScore: analysis.calculateAccessibilityScore(accessibility.issues, criticalElements),
            issues: accessibility.issues,
            recommendations: analysis.generateRecommendations(accessibility.issues, criticalElements),
            criticalElements,
          };
          
          components.push(component);
        });
      });
      
      return components;
    });
  }

  /**
   * Analyze reviews
   */
  private async analyzeReviews(page: Page): Promise<EcommerceComponent[]> {
    return await page.evaluate(() => {
      const components: EcommerceComponent[] = [];
      
      const reviewSelectors = [
        '.reviews',
        '.product-reviews',
        '[class*="review"]',
        '.testimonials',
        '.ratings',
      ];
      
      reviewSelectors.forEach(selector => {
        const reviews = document.querySelectorAll(selector);
        reviews.forEach(review => {
          const element = review as HTMLElement;
          const analysis = (window as any).wcagEcommerceAnalysis;
          const accessibility = analysis.isElementAccessible(element);
          const criticalElements = analysis.analyzeCriticalElements(element);
          
          const component: EcommerceComponent = {
            type: 'review',
            selector: analysis.getElementSelector(element),
            isAccessible: accessibility.accessible,
            accessibilityScore: analysis.calculateAccessibilityScore(accessibility.issues, criticalElements),
            issues: accessibility.issues,
            recommendations: analysis.generateRecommendations(accessibility.issues, criticalElements),
            criticalElements,
          };
          
          components.push(component);
        });
      });
      
      return components;
    });
  }

  /**
   * Analyze checkout flow accessibility
   */
  private async analyzeCheckoutFlowAccessibility(page: Page): Promise<{
    steps: string[];
    accessibilityIssues: string[];
    recommendations: string[];
  }> {
    return await page.evaluate(() => {
      const steps: string[] = [];
      const accessibilityIssues: string[] = [];
      const recommendations: string[] = [];

      // Detect checkout steps
      const stepSelectors = [
        '.checkout-step',
        '.step',
        '[class*="step-"]',
        '.checkout-progress',
      ];

      stepSelectors.forEach(selector => {
        const stepElements = document.querySelectorAll(selector);
        stepElements.forEach((step, index) => {
          steps.push(`Step ${index + 1}: ${step.textContent?.trim() || 'Unnamed step'}`);
        });
      });

      // Check for common accessibility issues in checkout
      if (!document.querySelector('form[action*="checkout"] label')) {
        accessibilityIssues.push('Checkout form fields missing labels');
        recommendations.push('Add proper labels to all checkout form fields');
      }

      if (!document.querySelector('[aria-live]')) {
        accessibilityIssues.push('No live regions for checkout updates');
        recommendations.push('Add aria-live regions for checkout status updates');
      }

      if (!document.querySelector('.error-message, .validation-error')) {
        // This might be good, but we should check if error handling exists
        recommendations.push('Ensure error messages are properly announced to screen readers');
      }

      return {
        steps,
        accessibilityIssues,
        recommendations,
      };
    });
  }

  /**
   * Analyze payment security
   */
  private async analyzePaymentSecurity(page: Page): Promise<{
    hasSecurePayment: boolean;
    accessibilityIssues: string[];
  }> {
    return await page.evaluate(() => {
      const accessibilityIssues: string[] = [];
      
      // Check for secure payment indicators
      const hasSSL = window.location.protocol === 'https:';
      const hasSecureIndicators = document.querySelector('[class*="secure"]') || 
                                 document.querySelector('[class*="ssl"]') ||
                                 document.querySelector('[class*="encrypted"]');

      if (!hasSSL) {
        accessibilityIssues.push('Payment page not served over HTTPS');
      }

      // Check for accessible security information
      if (!document.querySelector('[aria-label*="secure"]') && 
          !document.querySelector('[title*="secure"]')) {
        accessibilityIssues.push('Security information not accessible to screen readers');
      }

      return {
        hasSecurePayment: hasSSL && !!hasSecureIndicators,
        accessibilityIssues,
      };
    });
  }

  /**
   * Analyze product accessibility
   */
  private async analyzeProductAccessibility(page: Page): Promise<{
    totalProducts: number;
    accessibleProducts: number;
    commonIssues: string[];
  }> {
    return await page.evaluate(() => {
      const productSelectors = [
        '.product',
        '[class*="product-"]',
        '.product-item',
        '.shop-item',
      ];

      let totalProducts = 0;
      let accessibleProducts = 0;
      const commonIssues: string[] = [];

      productSelectors.forEach(selector => {
        const products = document.querySelectorAll(selector);
        products.forEach(product => {
          totalProducts++;
          
          const analysis = (window as any).wcagEcommerceAnalysis;
          const accessibility = analysis.isElementAccessible(product as HTMLElement);
          
          if (accessibility.accessible) {
            accessibleProducts++;
          } else {
            accessibility.issues.forEach((issue: string) => {
              if (!commonIssues.includes(issue)) {
                commonIssues.push(issue);
              }
            });
          }
        });
      });

      return {
        totalProducts,
        accessibleProducts,
        commonIssues,
      };
    });
  }

  /**
   * Calculate overall results
   */
  private calculateOverallResults(
    platform: string,
    components: EcommerceComponent[],
    checkoutFlow: any,
    paymentSecurity: any,
    productAccessibility: any
  ): EcommerceAnalysisResult {
    const totalComponents = components.length;
    const accessibleComponents = components.filter(c => c.isAccessible).length;
    const averageScore = totalComponents > 0 
      ? components.reduce((sum, c) => sum + c.accessibilityScore, 0) / totalComponents 
      : 100;

    const criticalIssues: string[] = [];
    const optimizations: string[] = [];

    // Collect critical issues
    components.forEach(component => {
      if (component.accessibilityScore < 50) {
        criticalIssues.push(`${component.type} has critical accessibility issues`);
      }
    });

    if (paymentSecurity.accessibilityIssues.length > 0) {
      criticalIssues.push(...paymentSecurity.accessibilityIssues);
    }

    // Generate optimizations
    optimizations.push(`Improve accessibility for ${totalComponents - accessibleComponents} components`);
    optimizations.push('Implement comprehensive product image alt text strategy');
    optimizations.push('Ensure all form elements have proper labels and error handling');
    optimizations.push('Add ARIA live regions for dynamic content updates');

    if (platform === 'Shopify') {
      optimizations.push('Use accessibility-focused Shopify themes like Dawn');
    } else if (platform === 'WooCommerce') {
      optimizations.push('Install WooCommerce accessibility plugins');
    }

    return {
      platform,
      components,
      checkoutFlow,
      paymentSecurity,
      productAccessibility,
      overallScore: Math.round(averageScore),
      criticalIssues,
      optimizations,
    };
  }
}

export default EcommerceOptimizer;
