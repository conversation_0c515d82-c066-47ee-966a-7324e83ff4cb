'use client';

/**
 * WCAG Manual Review Submission Page
 * Allows users to select a scan and submit manual reviews
 */

import React, { useState, useEffect } from 'react';
import { useRouter, useSearchParams } from 'next/navigation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  ArrowLeft,
  ClipboardCheck,
  Calendar,
  AlertTriangle,
  CheckCircle,
  ExternalLink,
} from 'lucide-react';
import { useWcagState, useWcagActions } from '@/context/WcagContext';
import { WcagProvider } from '@/context/WcagContext';
import { WcagBreadcrumb } from '@/components/navigation/WcagBreadcrumb';
import WcagManualReviewDashboard from '@/components/wcag/WcagManualReviewDashboard';

/**
 * WCAG Manual Review Submission Content Component
 */
const WcagManualReviewSubmissionContent: React.FC = () => {
  const router = useRouter();
  const searchParams = useSearchParams();
  const state = useWcagState();
  const actions = useWcagActions();
  const [mounted, setMounted] = useState(false);
  const [selectedScanId, setSelectedScanId] = useState<string>('');
  const [selectedScan, setSelectedScan] = useState<any>(null);

  // Handle client-side mounting
  useEffect(() => {
    setMounted(true);
  }, []);

  // Load scans with manual review items
  useEffect(() => {
    const loadScans = async () => {
      try {
        await actions.fetchScans({ status: 'completed', limit: 50 });
      } catch (error) {
        console.error('Error loading scans:', error);
      }
    };

    if (mounted) {
      loadScans();
    }
  }, [mounted, actions]);

  // Check for pre-selected scan from URL params
  useEffect(() => {
    const scanId = searchParams.get('scanId');
    if (scanId && state.scans.length > 0) {
      const scan = state.scans.find(s => s.scanId === scanId);
      if (scan && scan.summary?.manualReviewItems > 0) {
        setSelectedScanId(scanId);
        setSelectedScan(scan);
      }
    }
  }, [searchParams, state.scans]);

  // Load selected scan details
  useEffect(() => {
    const loadScanDetails = async () => {
      if (selectedScanId && !selectedScan) {
        try {
          const scanResult = await actions.fetchScanDetails(selectedScanId);
          setSelectedScan(scanResult);
        } catch (error) {
          console.error('Error loading scan details:', error);
        }
      }
    };

    loadScanDetails();
  }, [selectedScanId, selectedScan, actions]);

  const handleBack = () => {
    router.push('/dashboard/wcag/manual-review');
  };

  const handleScanSelect = async (scanId: string) => {
    setSelectedScanId(scanId);
    setSelectedScan(null); // Reset to trigger loading
    
    try {
      const scanResult = await actions.fetchScanDetails(scanId);
      setSelectedScan(scanResult);
    } catch (error) {
      console.error('Error loading scan details:', error);
    }
  };

  const handleReviewUpdate = (scanId: string, updatedScore: number) => {
    console.log(`Manual review updated for scan ${scanId}, new score: ${updatedScore}%`);
    // Optionally refresh the scan data or show success message
  };

  // Filter scans that have manual review items
  const scansWithManualReview = state.scans.filter(
    (scan) => scan.status === 'completed' && scan.summary?.manualReviewItems > 0
  );

  const getScoreBadge = (score: number) => {
    if (score >= 90) return <Badge variant="default">Excellent</Badge>;
    if (score >= 70) return <Badge variant="secondary">Good</Badge>;
    if (score >= 50) return <Badge variant="outline">Fair</Badge>;
    return <Badge variant="destructive">Poor</Badge>;
  };

  if (!mounted) {
    return (
      <div className="flex items-center justify-center min-h-[400px]">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Breadcrumb */}
      <WcagBreadcrumb />

      {/* Header */}
      <div className="flex items-center gap-4">
        <Button variant="outline" size="sm" onClick={handleBack}>
          <ArrowLeft className="h-4 w-4" />
        </Button>
        <div>
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white">Submit Manual Review</h1>
          <p className="text-gray-600 dark:text-gray-400 mt-1">
            Select a scan and complete manual accessibility reviews
          </p>
        </div>
      </div>

      {/* Error Alert */}
      {state.error && (
        <Alert variant="destructive">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{state.error}</AlertDescription>
        </Alert>
      )}

      {/* Scan Selection */}
      {!selectedScan && (
        <Card>
          <CardHeader>
            <CardTitle>Select Scan for Manual Review</CardTitle>
          </CardHeader>
          <CardContent>
            {scansWithManualReview.length === 0 ? (
              <div className="text-center py-8">
                <ClipboardCheck className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <p className="text-gray-500 dark:text-gray-400 mb-2">
                  No scans require manual review
                </p>
                <p className="text-sm text-gray-400 mb-4">
                  All completed scans have been fully automated or reviews are complete.
                </p>
                <Button onClick={() => router.push('/dashboard/wcag/scan')}>
                  Start New Scan
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                <div>
                  <label className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-2 block">
                    Choose a scan to review
                  </label>
                  <Select value={selectedScanId} onValueChange={handleScanSelect}>
                    <SelectTrigger>
                      <SelectValue placeholder="Select a scan..." />
                    </SelectTrigger>
                    <SelectContent>
                      {scansWithManualReview.map((scan) => (
                        <SelectItem key={scan.scanId} value={scan.scanId}>
                          <div className="flex items-center justify-between w-full">
                            <span className="truncate">{scan.url || scan.targetUrl}</span>
                            <Badge variant="outline" className="ml-2">
                              {scan.summary?.manualReviewItems || 0} items
                            </Badge>
                          </div>
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Scan List */}
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900 dark:text-white">Available Scans:</h4>
                  {scansWithManualReview.map((scan) => (
                    <div
                      key={scan.scanId}
                      className={`flex items-center justify-between p-4 border rounded-lg cursor-pointer transition-colors ${
                        selectedScanId === scan.scanId
                          ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20'
                          : 'hover:bg-gray-50 dark:hover:bg-gray-800'
                      }`}
                      onClick={() => handleScanSelect(scan.scanId)}
                    >
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2 mb-1">
                          <p className="font-medium text-gray-900 dark:text-white truncate">
                            {scan.url || scan.targetUrl}
                          </p>
                          <Badge variant="outline">{scan.summary?.manualReviewItems || 0} items</Badge>
                        </div>
                        <div className="flex items-center gap-4 text-sm text-gray-500 dark:text-gray-400">
                          <span className="flex items-center gap-1">
                            <Calendar className="h-3 w-3" />
                            {new Date(scan.scanTimestamp).toLocaleDateString()}
                          </span>
                          <span>WCAG {scan.wcagVersion}</span>
                          <span>Level {scan.complianceLevel}</span>
                        </div>
                      </div>
                      <div className="flex items-center gap-4">
                        <div className="text-right">
                          <p className="font-medium text-gray-900 dark:text-white">
                            {scan.overallScore ? Number(scan.overallScore).toFixed(1) : '0.0'}%
                          </p>
                          {getScoreBadge(Number(scan.overallScore) || 0)}
                        </div>
                        <ExternalLink className="h-4 w-4 text-gray-400" />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Manual Review Dashboard */}
      {selectedScan && (
        <div className="space-y-4">
          {/* Selected Scan Info */}
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="font-medium text-gray-900 dark:text-white">
                    Reviewing: {selectedScan.url || selectedScan.targetUrl}
                  </h3>
                  <p className="text-sm text-gray-500 dark:text-gray-400">
                    {selectedScan.summary?.manualReviewItems || 0} items requiring manual review
                  </p>
                </div>
                <Button variant="outline" size="sm" onClick={() => setSelectedScan(null)}>
                  Change Scan
                </Button>
              </div>
            </CardContent>
          </Card>

          {/* Manual Review Component */}
          <WcagManualReviewDashboard
            scanResult={selectedScan}
            onReviewUpdate={handleReviewUpdate}
          />
        </div>
      )}
    </div>
  );
};

/**
 * Main WCAG Manual Review Submission Page with Provider
 */
export default function WcagManualReviewSubmissionPage() {
  return (
    <WcagProvider>
      <WcagManualReviewSubmissionContent />
    </WcagProvider>
  );
}
